#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية قاعدة البيانات
"""

import sqlite3
from datetime import datetime

def examine_database_structure():
    """فحص بنية قاعدة البيانات"""
    print("🔍 فحص بنية قاعدة البيانات...")
    
    try:
        # الاتصال المباشر بقاعدة البيانات
        conn = sqlite3.connect('stores.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("الجداول الموجودة:")
        for table in tables:
            table_name = table['name']
            print(f"  - {table_name}")
            
            # عرض عدد السجلات في كل جدول
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                print(f"    عدد السجلات: {count}")
            except Exception as e:
                print(f"    خطأ في عد السجلات: {e}")
        
        # البحث عن جداول المعاملات
        transaction_tables = [name for name in [t['name'] for t in tables] 
                            if 'transaction' in name.lower() or 'معاملة' in name]
        
        print(f"\nجداول المعاملات المحتملة: {transaction_tables}")
        
        # فحص كل جدول للبحث عن البيانات المعطوبة
        for table in tables:
            table_name = table['name']
            print(f"\n=== فحص جدول {table_name} ===")
            
            try:
                # الحصول على بنية الجدول
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                print("الأعمدة:")
                date_columns = []
                for col in columns:
                    col_name = col['name']
                    col_type = col['type']
                    print(f"  - {col_name} ({col_type})")
                    
                    # البحث عن أعمدة التاريخ
                    if 'date' in col_name.lower() or 'time' in col_name.lower() or 'created' in col_name.lower():
                        date_columns.append(col_name)
                
                # فحص أعمدة التاريخ للبحث عن البيانات المعطوبة
                if date_columns:
                    print(f"أعمدة التاريخ: {date_columns}")
                    
                    for date_col in date_columns:
                        try:
                            # البحث عن البيانات المعطوبة
                            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE {date_col} LIKE '%06 17:47:37%'")
                            corrupted_count = cursor.fetchone()['count']
                            
                            if corrupted_count > 0:
                                print(f"  ⚠️ {corrupted_count} سجل معطوب في عمود {date_col}")
                                
                                # عرض عينة من البيانات المعطوبة
                                cursor.execute(f"SELECT id, {date_col} FROM {table_name} WHERE {date_col} LIKE '%06 17:47:37%' LIMIT 3")
                                corrupted_samples = cursor.fetchall()
                                
                                for sample in corrupted_samples:
                                    print(f"    ID: {sample['id']}, {date_col}: {repr(sample[date_col])}")
                            else:
                                print(f"  ✅ لا توجد بيانات معطوبة في عمود {date_col}")
                                
                        except Exception as e:
                            print(f"  خطأ في فحص عمود {date_col}: {e}")
                
            except Exception as e:
                print(f"خطأ في فحص جدول {table_name}: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def fix_corrupted_data_in_all_tables():
    """إصلاح البيانات المعطوبة في جميع الجداول"""
    print("\n🔧 إصلاح البيانات المعطوبة في جميع الجداول...")
    
    try:
        conn = sqlite3.connect('stores.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # الحصول على جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_fixed = 0
        
        for table in tables:
            table_name = table['name']
            
            try:
                # الحصول على أعمدة الجدول
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                # البحث عن أعمدة التاريخ
                date_columns = []
                for col in columns:
                    col_name = col['name']
                    if 'date' in col_name.lower() or 'time' in col_name.lower() or 'created' in col_name.lower():
                        date_columns.append(col_name)
                
                # إصلاح البيانات المعطوبة في كل عمود تاريخ
                for date_col in date_columns:
                    try:
                        # البحث عن السجلات المعطوبة
                        cursor.execute(f"SELECT id FROM {table_name} WHERE {date_col} LIKE '%06 17:47:37%'")
                        corrupted_ids = [row['id'] for row in cursor.fetchall()]
                        
                        if corrupted_ids:
                            print(f"إصلاح {len(corrupted_ids)} سجل في جدول {table_name}, عمود {date_col}")
                            
                            # إصلاح كل سجل
                            for record_id in corrupted_ids:
                                cursor.execute(f"UPDATE {table_name} SET {date_col} = ? WHERE id = ?", 
                                             (current_date, record_id))
                                total_fixed += 1
                            
                            print(f"  ✅ تم إصلاح {len(corrupted_ids)} سجل")
                        
                    except Exception as e:
                        print(f"  خطأ في إصلاح عمود {date_col}: {e}")
                
            except Exception as e:
                print(f"خطأ في معالجة جدول {table_name}: {e}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print(f"\n✅ تم إصلاح {total_fixed} سجل إجمالي")
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")

def test_data_loading():
    """اختبار تحميل البيانات بعد الإصلاح"""
    print("\n🧪 اختبار تحميل البيانات بعد الإصلاح...")
    
    try:
        from database import db_manager
        
        # اختبار جميع الجداول الرئيسية
        test_queries = [
            ("المعاملات", "SELECT * FROM transactions LIMIT 5"),
            ("عمليات الصرف", "SELECT * FROM dispensing_operations LIMIT 5"),
            ("المستفيدين", "SELECT * FROM beneficiaries LIMIT 5"),
            ("الأصناف", "SELECT * FROM added_items LIMIT 5"),
            ("الجدول التنظيمي", "SELECT * FROM organizational_chart LIMIT 5"),
            ("حركات المخزون", "SELECT * FROM inventory_movements_new LIMIT 5")
        ]
        
        for test_name, query in test_queries:
            try:
                result = db_manager.fetch_all(query)
                print(f"✅ {test_name}: تم تحميل {len(result)} سجل")
            except Exception as e:
                print(f"❌ {test_name}: {e}")
                
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    print("🚀 فحص وإصلاح قاعدة البيانات")
    print("=" * 60)
    
    # فحص بنية قاعدة البيانات
    examine_database_structure()
    
    # إصلاح البيانات المعطوبة
    fix_corrupted_data_in_all_tables()
    
    # اختبار تحميل البيانات
    test_data_loading()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الفحص والإصلاح")
