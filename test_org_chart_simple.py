#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مبسط لشاشة الجدول التنظيمي
Simple Test for Organizational Chart Window

اختبار مباشر لفتح الشاشة واختبار الوظائف
"""

import sys
import os
from pathlib import Path
import tkinter as tk
import ttkbootstrap as ttk_bs
import pandas as pd
import time

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from database import db_manager
    from models import OrganizationalChart
    from ui.organizational_chart_window import OrganizationalChartWindow
    from utils.organizational_chart_import import import_organizational_chart_from_excel
    print("✅ تم استيراد جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def check_database_status():
    """فحص حالة قاعدة البيانات"""
    print("🔍 فحص حالة قاعدة البيانات...")
    
    try:
        # فحص الاتصال
        result = db_manager.fetch_one("SELECT 1")
        print("✅ الاتصال بقاعدة البيانات يعمل")
        
        # فحص البيانات
        total_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        active_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        inactive_count = total_count - active_count
        
        print(f"📊 حالة البيانات:")
        print(f"    - إجمالي: {total_count}")
        print(f"    - نشط: {active_count}")
        print(f"    - غير نشط: {inactive_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def create_test_excel_file():
    """إنشاء ملف Excel للاختبار"""
    print("\n📄 إنشاء ملف Excel للاختبار...")
    
    try:
        test_data = [
            {
                'التسلسل': 1,
                'رقم الصنف': 'SIMPLE001',
                'اسم الصنف': 'صنف اختبار بسيط 1',
                'اسم المعدة': 'قطعة',
                'الكمية': 5,
                'الملاحظات': 'اختبار بسيط'
            },
            {
                'التسلسل': 2,
                'رقم الصنف': 'SIMPLE002',
                'اسم الصنف': 'صنف اختبار بسيط 2',
                'اسم المعدة': 'كيلو',
                'الكمية': 10,
                'الملاحظات': 'اختبار بسيط'
            },
            {
                'التسلسل': 3,
                'رقم الصنف': 'SIMPLE003',
                'اسم الصنف': 'صنف اختبار بسيط 3',
                'اسم المعدة': 'متر',
                'الكمية': 8,
                'الملاحظات': 'اختبار بسيط'
            }
        ]
        
        df = pd.DataFrame(test_data)
        excel_file = project_root / "simple_test.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف Excel: {excel_file}")
        print(f"📊 عدد الصفوف: {len(test_data)}")
        
        return excel_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {e}")
        return None

def test_import_function(excel_file):
    """اختبار وظيفة الاستيراد"""
    print("\n📥 اختبار وظيفة الاستيراد...")
    
    try:
        # حالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 عدد العناصر قبل الاستيراد: {before_count}")
        
        # تشغيل الاستيراد
        result = import_organizational_chart_from_excel(str(excel_file))
        
        # حالة بعد الاستيراد
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 عدد العناصر بعد الاستيراد: {after_count}")
        
        # النتائج
        print(f"📊 نتائج الاستيراد:")
        print(f"    ✅ نجح: {result.success_count}")
        print(f"    🔄 مكرر: {result.duplicate_count}")
        print(f"    ❌ خطأ: {result.error_count}")
        print(f"    ⏱️ وقت المعالجة: {result.processing_time:.2f} ثانية")
        
        return result.success_count > 0 or result.duplicate_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        return False

def test_delete_all_function():
    """اختبار وظيفة حذف جميع الأصناف"""
    print("\n🗑️ اختبار وظيفة حذف جميع الأصناف...")
    
    try:
        # حالة قبل الحذف
        before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        before_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        
        print(f"📊 قبل الحذف:")
        print(f"    - عناصر نشطة: {before_active}")
        print(f"    - إجمالي العناصر: {before_total}")
        
        if before_active == 0:
            print("⚠️ لا توجد عناصر نشطة للحذف")
            return True
        
        # تشغيل الحذف
        delete_result = OrganizationalChart.delete_all()
        
        # حالة بعد الحذف
        after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        after_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        
        print(f"📊 بعد الحذف:")
        print(f"    - عناصر نشطة: {after_active}")
        print(f"    - إجمالي العناصر: {after_total}")
        print(f"    - تم حذف: {before_active - after_active} عنصر")
        print(f"    - نتيجة العملية: {delete_result}")
        
        return delete_result is not False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحذف: {e}")
        return False

def test_org_chart_window():
    """اختبار فتح شاشة الجدول التنظيمي"""
    print("\n🖼️ اختبار فتح شاشة الجدول التنظيمي...")
    
    try:
        # إنشاء النافذة الرئيسية
        print("🔧 إنشاء النافذة الرئيسية...")
        main_window = ttk_bs.Window(
            title="اختبار شاشة الجدول التنظيمي",
            themename="cosmo",
            size=(1200, 800)
        )
        
        # إخفاء النافذة الرئيسية مؤقتاً
        main_window.withdraw()
        
        print("🔧 محاولة إنشاء شاشة الجدول التنظيمي...")
        
        # إنشاء شاشة الجدول التنظيمي
        org_chart_window = OrganizationalChartWindow(main_window, None)
        
        # التحقق من إنشاء الشاشة
        if hasattr(org_chart_window, 'window') and org_chart_window.window:
            print("✅ تم إنشاء شاشة الجدول التنظيمي بنجاح")
            
            # التحقق من تحميل البيانات
            if hasattr(org_chart_window, 'tree'):
                tree_items = len(org_chart_window.tree.get_children())
                print(f"📊 عدد العناصر في الجدول: {tree_items}")
            
            # التحقق من وجود الأزرار المهمة
            if hasattr(org_chart_window, 'import_btn'):
                print("✅ زر الاستيراد موجود")
            
            if hasattr(org_chart_window, 'delete_all_btn'):
                print("✅ زر حذف جميع الأصناف موجود")
            
            # عرض النافذة
            main_window.deiconify()
            org_chart_window.window.lift()
            org_chart_window.window.focus_force()
            
            print("✅ الشاشة مفتوحة ومتاحة للاستخدام")
            print("⚠️ يمكنك الآن اختبار الوظائف يدوياً")
            print("📝 اضغط Enter للمتابعة أو أغلق النافذة...")
            
            # انتظار إدخال المستخدم أو إغلاق النافذة
            def wait_for_user():
                try:
                    input()  # انتظار Enter
                except:
                    pass
                finally:
                    try:
                        main_window.quit()
                    except:
                        pass
            
            import threading
            wait_thread = threading.Thread(target=wait_for_user, daemon=True)
            wait_thread.start()
            
            # تشغيل الحلقة الرئيسية
            try:
                main_window.mainloop()
            except:
                pass
            
            print("✅ تم إغلاق الشاشة")
            return True
            
        else:
            print("❌ فشل في إنشاء شاشة الجدول التنظيمي")
            main_window.destroy()
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الشاشة: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    print("\n🧹 تنظيف بيانات الاختبار...")
    
    try:
        # حذف ملف Excel
        excel_file = project_root / "simple_test.xlsx"
        if excel_file.exists():
            excel_file.unlink()
            print("🗑️ تم حذف ملف Excel")
        
        # حذف بيانات الاختبار من قاعدة البيانات
        deleted_count = db_manager.execute_query(
            "DELETE FROM organizational_chart WHERE item_code LIKE 'SIMPLE%'"
        ).rowcount
        
        if deleted_count > 0:
            print(f"🗑️ تم حذف {deleted_count} عنصر اختبار من قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في التنظيف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار مبسط لشاشة الجدول التنظيمي")
    print("=" * 50)
    
    tests = [
        ("فحص قاعدة البيانات", check_database_status),
        ("إنشاء ملف Excel", create_test_excel_file),
        ("اختبار الاستيراد", lambda: test_import_function(create_test_excel_file())),
        ("اختبار حذف جميع الأصناف", test_delete_all_function),
        ("اختبار فتح الشاشة", test_org_chart_window),
        ("تنظيف البيانات", cleanup_test_data)
    ]
    
    successful = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "اختبار الاستيراد":
                # اختبار خاص للاستيراد
                excel_file = create_test_excel_file()
                if excel_file:
                    result = test_import_function(excel_file)
                else:
                    result = False
            else:
                result = test_func()
            
            if result:
                successful += 1
                print(f"✅ {test_name} - نجح")
            else:
                print(f"❌ {test_name} - فشل")
                
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        
        time.sleep(1)
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج")
    print("=" * 50)
    
    success_rate = (successful / total) * 100
    print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful}/{total})")
    
    if success_rate >= 80:
        print("🎉 الاختبار ممتاز - النظام يعمل بشكل جيد!")
    elif success_rate >= 60:
        print("⚠️ الاختبار جيد - توجد بعض المشاكل البسيطة")
    else:
        print("❌ الاختبار ضعيف - توجد مشاكل تحتاج إلى إصلاح")
    
    return 0 if success_rate >= 60 else 1

if __name__ == "__main__":
    sys.exit(main())