#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جدول المعاملات
"""

import sqlite3
from datetime import datetime

def examine_transactions_table():
    """فحص جدول المعاملات"""
    print("🔍 فحص جدول المعاملات...")
    
    try:
        # الاتصال المباشر بقاعدة البيانات
        conn = sqlite3.connect('stores.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("الجداول الموجودة:")
        transaction_tables = []
        for table in tables:
            table_name = table['name']
            print(f"  - {table_name}")
            if 'transaction' in table_name.lower():
                transaction_tables.append(table_name)
        
        print(f"\nجداول المعاملات: {transaction_tables}")
        
        # فحص كل جدول معاملات
        for table_name in transaction_tables:
            print(f"\n=== فحص جدول {table_name} ===")
            
            try:
                # عرض بنية الجدول
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                print("الأعمدة:")
                for col in columns:
                    print(f"  - {col['name']} ({col['type']})")
                
                # عد السجلات
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                print(f"عدد السجلات: {count}")
                
                if count > 0:
                    # عرض عينة من البيانات
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    samples = cursor.fetchall()
                    
                    print("عينة من البيانات:")
                    for i, sample in enumerate(samples):
                        print(f"  سجل {i+1}:")
                        for key in sample.keys():
                            value = sample[key]
                            print(f"    {key}: {repr(value)} (نوع: {type(value)})")
                        print()
                
            except Exception as e:
                print(f"خطأ في فحص جدول {table_name}: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def fix_transactions_data():
    """إصلاح بيانات المعاملات"""
    print("\n🔧 إصلاح بيانات المعاملات...")

    try:
        # محاولة الاتصال بقاعدة البيانات الصحيحة
        db_paths = [
            'stores.db',
            'stores_management.db',
            'database.db'
        ]

        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # اختبار الاتصال
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                tables = cursor.fetchall()

                if tables:
                    print(f"✅ تم الاتصال بقاعدة البيانات: {db_path}")
                    break
                else:
                    conn.close()
                    conn = None
            except Exception as e:
                if conn:
                    conn.close()
                conn = None
                print(f"❌ فشل الاتصال بـ {db_path}: {e}")

        if not conn:
            print("❌ لم يتم العثور على قاعدة بيانات صالحة")
            return

        cursor = conn.cursor()

        # البحث عن جدول المعاملات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = [row['name'] for row in cursor.fetchall()]
        print(f"جميع الجداول: {all_tables}")

        transaction_tables = [table for table in all_tables if 'transaction' in table.lower()]

        if not transaction_tables:
            print("❌ لم يتم العثور على جداول المعاملات")
            return

        current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_fixed = 0

        for table_name in transaction_tables:
            print(f"\nمعالجة جدول {table_name}...")

            try:
                # الحصول على أعمدة الجدول
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()

                # البحث عن أعمدة التاريخ
                date_columns = []
                for col in columns:
                    col_name = col['name']
                    if 'date' in col_name.lower() or 'time' in col_name.lower():
                        date_columns.append(col_name)

                print(f"أعمدة التاريخ: {date_columns}")

                # فحص وإصلاح كل عمود تاريخ
                for date_col in date_columns:
                    print(f"  فحص عمود {date_col}...")

                    # الحصول على جميع السجلات
                    cursor.execute(f"SELECT id, {date_col} FROM {table_name}")
                    all_records = cursor.fetchall()

                    corrupted_ids = []

                    for record in all_records:
                        try:
                            date_value = record[date_col]
                            is_corrupted = False

                            # فحص إذا كان التاريخ معطوب
                            if date_value:
                                # فحص البيانات الثنائية
                                if isinstance(date_value, bytes):
                                    is_corrupted = True
                                    print(f"    بيانات ثنائية في السجل {record['id']}: {repr(date_value)}")
                                elif isinstance(date_value, str):
                                    # فحص البيانات المعطوبة مثل b'06 17:47:37.387051'
                                    if (date_value.startswith("b'") or
                                        date_value.startswith('b"') or
                                        not date_value.replace('-', '').replace(':', '').replace(' ', '').replace('.', '').isdigit()):
                                        is_corrupted = True
                                        print(f"    تاريخ معطوب في السجل {record['id']}: {repr(date_value)}")
                                    else:
                                        # محاولة تحليل التاريخ
                                        try:
                                            test_date = date_value
                                            if '.' in test_date:
                                                test_date = test_date.split('.')[0]
                                            datetime.strptime(test_date, '%Y-%m-%d %H:%M:%S')
                                        except ValueError:
                                            is_corrupted = True
                                            print(f"    تاريخ غير صالح في السجل {record['id']}: {repr(date_value)}")
                                else:
                                    # نوع البيانات خاطئ
                                    is_corrupted = True
                                    print(f"    نوع بيانات خاطئ في السجل {record['id']}: {repr(date_value)}")

                            if is_corrupted:
                                corrupted_ids.append(record['id'])

                        except Exception as e:
                            corrupted_ids.append(record['id'])
                            print(f"    خطأ في فحص السجل {record['id']}: {e}")

                    # إصلاح السجلات المعطوبة
                    if corrupted_ids:
                        print(f"  إصلاح {len(corrupted_ids)} سجل معطوب...")

                        for record_id in corrupted_ids:
                            try:
                                cursor.execute(f"UPDATE {table_name} SET {date_col} = ? WHERE id = ?",
                                             (current_date, record_id))
                                total_fixed += 1
                            except Exception as e:
                                print(f"    فشل في إصلاح السجل {record_id}: {e}")

                        print(f"  ✅ تم إصلاح {len(corrupted_ids)} سجل في عمود {date_col}")
                    else:
                        print(f"  ✅ لا توجد بيانات معطوبة في عمود {date_col}")

            except Exception as e:
                print(f"خطأ في معالجة جدول {table_name}: {e}")

        # حفظ التغييرات
        conn.commit()
        conn.close()

        print(f"\n✅ تم إصلاح {total_fixed} سجل إجمالي")

    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")

def test_transactions_loading():
    """اختبار تحميل المعاملات بعد الإصلاح"""
    print("\n🧪 اختبار تحميل المعاملات بعد الإصلاح...")
    
    try:
        from database import db_manager
        
        # اختبار الاستعلام الذي يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        try:
            transactions = db_manager.fetch_all(transactions_query)
            print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح")
            
            # عرض عينة من البيانات
            for i, trans in enumerate(transactions):
                print(f"  معاملة {i+1}:")
                print(f"    التاريخ: {trans['transaction_date']}")
                print(f"    الرقم: {trans['transaction_number']}")
                print(f"    المستفيد: {trans['beneficiary_name']}")
                print(f"    عدد الأصناف: {trans['items_count']}")
                print()
                
        except Exception as e:
            print(f"❌ فشل في تحميل المعاملات: {e}")
        
        # اختبار دالة get_real_activities
        print("اختبار دالة get_real_activities...")
        try:
            # محاكاة الدالة
            from ui.main_window import MainWindow
            
            # إنشاء كائن وهمي للاختبار
            class TestWindow:
                def get_real_activities(self):
                    try:
                        from database import db_manager
                        activities = []

                        # اختبار استعلام المعاملات
                        transactions_query = """
                            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                                   COUNT(ti.id) as items_count
                            FROM transactions t
                            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                            WHERE t.status = 'مكتملة'
                            GROUP BY t.id
                            ORDER BY t.transaction_date DESC, t.id DESC
                            LIMIT 5
                        """

                        transactions = db_manager.fetch_all(transactions_query)
                        for trans in transactions:
                            try:
                                date_str = trans['transaction_date']
                                if isinstance(date_str, str):
                                    from datetime import datetime
                                    try:
                                        # محاولة تحليل التاريخ مع الوقت الكامل
                                        if '.' in date_str:
                                            # إزالة الـ microseconds
                                            date_str = date_str.split('.')[0]
                                        
                                        if ' ' in date_str:
                                            trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                                        else:
                                            trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                                        
                                        time_str = trans_date.strftime('%H:%M')
                                        date_display = trans_date.strftime('%m/%d')
                                    except ValueError as e:
                                        print(f"خطأ في معالجة التاريخ: {e}")
                                        time_str = "00:00"
                                        date_display = "اليوم"
                                else:
                                    time_str = "00:00"
                                    date_display = "اليوم"

                                beneficiary = trans['beneficiary_name'] or "غير محدد"
                                try:
                                    items_count = int(trans['items_count']) if trans['items_count'] else 0
                                except (ValueError, TypeError):
                                    items_count = 0

                                activity = f"🔄 {date_display} {time_str} - تم صرف {items_count} صنف للمستفيد {beneficiary}"
                                activities.append(activity)
                            except Exception as e:
                                print(f"خطأ في معالجة عملية الصرف: {e}")

                        return activities[:5]

                    except Exception as e:
                        print(f"خطأ في الحصول على الأنشطة: {e}")
                        return []
            
            test_window = TestWindow()
            activities = test_window.get_real_activities()
            
            print(f"✅ تم تحميل {len(activities)} نشاط")
            for activity in activities:
                print(f"  - {activity}")
                
        except Exception as e:
            print(f"❌ فشل في اختبار get_real_activities: {e}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    print("🚀 إصلاح جدول المعاملات")
    print("=" * 60)
    
    # فحص جدول المعاملات
    examine_transactions_table()
    
    # إصلاح البيانات المعطوبة
    fix_transactions_data()
    
    # اختبار تحميل المعاملات
    test_transactions_loading()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الإصلاح")
