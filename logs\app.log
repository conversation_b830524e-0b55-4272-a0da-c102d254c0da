2025-07-02 17:16:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 17:16:16 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 17:17:18 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 17:23:20 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 21:43:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 21:43:20 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 21:44:10 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 21:44:36 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'root'
2025-07-02 21:47:23 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:32 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:34 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:58 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:47:59 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:25 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 21:50:07 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 21:50:14 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 21:51:38 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 21:51:42 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:25 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 21:53:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 21:53:34 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:38 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:16:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 22:16:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 22:16:36 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:38 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:44 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:49 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:52 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:55 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:56 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:27:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-02 22:27:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-02 22:30:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:44 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:30:45 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:31:00 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 05:29:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 05:29:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 05:41:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 05:41:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 05:41:56 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:42:08 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:43:18 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:43:46 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:48:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 05:48:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 05:54:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 05:54:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 05:54:28 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:54:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:54:42 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:55:36 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:58:47 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 05:58:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 05:58:59 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:00 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:02 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:07 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 06:02:05 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:02:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:02:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:02:45 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:03:27 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:04:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:04:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:04:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:04:42 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:00 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:03 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:05 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:08:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:08:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:10:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:11:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:11:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:12:10 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض الأصناف المرتبطة: bad anchor "right": must be n, ne, e, se, s, sw, w, nw, or center
2025-07-03 06:12:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:12:25 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:12:30 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:13:29 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:14:13 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:03 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:15:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:15:15 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:18 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:16:45 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:16:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:17:02 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:17:11 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:17:19 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:22:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:22:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:28:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:28:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:35:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:35:13 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:38:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:38:56 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:41:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:41:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:45:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:45:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:46:29 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:46:41 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:46:47 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:54:17 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-03 06:54:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-03 06:54:31 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض الأصناف المرتبطة: bad anchor "right": must be n, ne, e, se, s, sw, w, nw, or center
2025-07-03 06:55:19 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:55:24 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-04 09:03:14 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:03:23 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:18:24 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:18:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:33:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:33:19 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:33:39 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:33:42 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:34:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:35:03 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:37:55 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:38:18 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:46:46 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:46:52 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:47:16 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:48:25 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:50:42 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:51:17 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:51:32 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:52:26 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:58:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 09:58:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 09:58:34 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:58:53 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:59:15 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:59:24 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:38 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:04:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:04:48 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:50 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:51 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:07 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:08 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:13 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:16 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:19 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:06:08 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:06:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:11:56 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:12:01 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:12:04 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:12:06 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:12:10 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:13:34 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:13:45 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:13:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:13:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:15:42 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:15:47 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:15:57 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:16:28 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:16:31 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:23:28 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:23:34 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:29:53 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-04 10:36:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:36:53 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:38:11 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:38:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 10:44:29 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-07-04 10:58:50 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 10:58:55 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:03:49 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 11:06:06 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:06:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:08:04 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:08:10 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:11:42 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:11:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:20:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:20:17 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:31:10 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:31:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel23.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel23.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:37:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-04 11:38:29 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:09:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:09:24 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:18:09 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:18:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:18:33 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:35 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:36 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:36 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:36 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:39 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:40 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:43 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:43 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:47 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:48 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:48 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:18:48 - StoresApp - ERROR - UI Error - خطأ: فشل في حفظ عملية الصرف
2025-07-05 05:21:15 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:21:22 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:31:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:31:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:34:29 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:34:35 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:44:01 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:44:09 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:46:02 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:46:08 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 05:51:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel3.!frame.!frame5.!treeview"
2025-07-05 05:51:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel7.!frame.!frame5.!treeview"
2025-07-05 05:51:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!frame.!frame5.!treeview"
2025-07-05 05:51:54 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel11.!frame.!frame5.!treeview"
2025-07-05 05:54:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 05:54:26 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 06:02:57 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 06:03:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 06:15:32 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 06:15:37 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 06:32:43 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 06:32:49 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 06:45:57 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 06:46:28 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 06:52:36 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 06:52:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 06:53:06 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:08:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:08:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:08:57 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:09:19 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:15:04 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:15:21 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:15:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:15:38 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:26:19 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:26:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:26:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:26:56 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:27:22 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:28:07 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:37:30 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:37:36 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:37:54 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:38:09 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:38:57 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:53:51 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:53:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:54:10 - StoresApp - ERROR - UI Error - خطأ: فشل في حذف العناصر
2025-07-05 07:54:14 - StoresApp - ERROR - UI Error - خطأ: فشل في حذف العناصر
2025-07-05 07:54:27 - StoresApp - ERROR - UI Error - خطأ: فشل في حذف العناصر
2025-07-05 07:55:02 - StoresApp - ERROR - UI Error - خطأ: فشل في حذف العناصر
2025-07-05 07:55:46 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:56:03 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:56:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 07:56:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 07:56:28 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:56:57 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 07:58:56 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:02:22 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 08:02:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 08:02:37 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:02:51 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:03:10 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:06:48 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'root'
2025-07-05 08:09:17 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:09:36 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:09:48 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:10:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:10:35 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'root'
2025-07-05 08:21:13 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 08:21:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 08:21:34 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:21:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:22:07 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:22:17 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 08:22:23 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:22:28 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:22:33 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 08:22:41 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 08:22:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 08:23:05 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 11:54:20 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 11:54:27 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 11:55:09 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 12:04:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 12:04:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 12:04:43 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:04:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:04:56 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 12:05:06 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 12:05:28 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 12:07:32 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:07:49 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 12:09:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 12:11:19 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:12:32 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم الأمن والسلامة'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:12:35 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم الأمن والسلامة'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:13:06 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم التسويق'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:13:08 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم الأمن والسلامة'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:14:39 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم التسويق'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:14:41 - StoresApp - ERROR - UI Error - لا يمكن الحذف: لا يمكن حذف القسم 'قسم الأمن والسلامة'
يوجد 1 مستفيد مرتبط بهذا القسم

يرجى نقل أو حذف المستفيدين أولاً
2025-07-05 12:17:06 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:17:36 - StoresApp - ERROR - UI Error - ❌ خطأ: حدث خطأ أثناء الحذف: 'MainWindow' object has no attribute 'window'
2025-07-05 12:17:39 - StoresApp - ERROR - UI Error - ❌ خطأ: حدث خطأ أثناء الحذف: 'MainWindow' object has no attribute 'window'
2025-07-05 12:19:17 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
يرجى اختيار الإدارة
2025-07-05 12:19:19 - StoresApp - ERROR - UI Error - خطأ في البيانات: يرجى اختيار الوحدة
يرجى اختيار الإدارة
2025-07-05 12:20:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 12:20:43 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 12:20:50 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:20:55 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 12:21:01 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'window'
2025-07-05 12:32:33 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 12:32:39 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 12:32:42 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:32:50 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 12:33:12 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:33:18 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:34:11 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:34:12 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:34:12 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:34:12 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:34:16 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:36:37 - StoresApp - ERROR - UI Error - ❌ خطأ: حدث خطأ أثناء الحذف: 'MainWindow' object has no attribute 'window'
2025-07-05 12:36:45 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 12:37:14 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 12:40:34 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 12:41:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 12:41:41 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 12:41:47 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:43:22 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:37 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:40 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:40 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:40 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:44 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:43:44 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:45 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:43:45 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 12:46:14 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 12:47:04 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 12:47:11 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 13:05:44 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 13:05:50 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 13:05:55 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:06:07 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:20 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:22 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:22 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:23 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:32 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-05 13:06:37 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:06:50 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 13:07:14 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:09:33 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:09:35 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:09:35 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:09:36 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:09:37 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:09:38 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:10:49 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 13:11:36 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 13:13:59 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 13:14:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 13:14:33 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-05 13:17:52 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:17:58 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:21:37 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-05 13:21:42 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-05 13:21:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-05 13:21:53 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:21:54 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء حذف البيانات: 'MainWindow' object has no attribute 'window'
2025-07-05 13:22:04 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: 'MainWindow' object has no attribute 'window'
2025-07-06 15:12:23 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 15:12:30 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 15:13:54 - StoresApp - ERROR - UI Error - خطأ: لم يتم حذف أي عنصر.

جميع العناصر مرتبطة بكميات في المخزون أو عمليات صرف.
2025-07-06 15:14:00 - StoresApp - ERROR - UI Error - خطأ: لم يتم حذف أي عنصر.

جميع العناصر مرتبطة بكميات في المخزون أو عمليات صرف.
2025-07-06 15:14:11 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ في معالجة النتائج: No module named 'ui.auto_success_message'
2025-07-06 15:22:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 15:22:15 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 15:22:42 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-06 15:33:48 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 15:33:54 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 15:44:52 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 15:44:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 15:46:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-06 15:56:00 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 15:56:05 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 16:04:53 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 16:04:58 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 16:05:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-06 16:14:07 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 16:14:21 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 16:15:00 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-06 16:21:23 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe8.!treeview"
2025-07-06 16:24:18 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 16:24:25 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 16:27:35 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 16:27:40 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
2025-07-06 17:30:12 - StoresApp - INFO - تم إعداد التطبيق بنجاح
2025-07-06 17:30:18 - StoresApp - INFO - تم تسجيل دخول المستخدم: admin
