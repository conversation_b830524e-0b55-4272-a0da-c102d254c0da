#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث المركز عن مصدر خطأ int() مع البيانات المعطوبة
"""

import traceback

def test_dashboard_directly():
    """اختبار مباشر لتحميل لوحة التحكم"""
    print("🧪 اختبار مباشر لتحميل لوحة التحكم...")
    
    try:
        # استيراد المكونات المطلوبة
        from database import db_manager
        from datetime import datetime
        
        print("✅ تم استيراد المكونات بنجاح")
        
        # اختبار الاستعلام الذي يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة")
        
        # فحص كل معاملة بالتفصيل
        for i, trans in enumerate(transactions):
            print(f"\n📋 معاملة {i+1}:")
            print(f"  transaction_date: {repr(trans['transaction_date'])} (نوع: {type(trans['transaction_date'])})")
            print(f"  items_count: {repr(trans['items_count'])} (نوع: {type(trans['items_count'])})")
            
            # محاولة معالجة التاريخ
            try:
                date_str = trans['transaction_date']
                print(f"  🔍 معالجة التاريخ: {repr(date_str)}")
                
                # فحص البيانات المعطوبة
                if date_str and isinstance(date_str, bytes):
                    print(f"    ❌ بيانات ثنائية: {repr(date_str)}")
                    try:
                        date_str = date_str.decode('utf-8')
                        print(f"    🔄 بعد فك التشفير: {repr(date_str)}")
                    except:
                        date_str = None
                        print(f"    ❌ فشل فك التشفير")
                
                # فحص البيانات المعطوبة في النص
                if date_str and isinstance(date_str, str):
                    if date_str.startswith("b'") or date_str.startswith('b"'):
                        print(f"    ❌ نص يحتوي على بيانات ثنائية: {repr(date_str)}")
                        date_str = None
                    elif not date_str.replace('-', '').replace(':', '').replace(' ', '').replace('.', '').isdigit():
                        print(f"    ❌ تاريخ غير صالح: {repr(date_str)}")
                        date_str = None
                
                if isinstance(date_str, str) and date_str:
                    if '.' in date_str:
                        date_str = date_str.split('.')[0]
                    
                    if ' ' in date_str:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        print(f"    ✅ تم تحليل التاريخ: {trans_date}")
                    else:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        print(f"    ✅ تم تحليل التاريخ: {trans_date}")
                else:
                    print(f"    ⚠️ تاريخ فارغ أو معطوب")
            
            except Exception as e:
                print(f"    ❌ خطأ في معالجة التاريخ: {e}")
                print(f"    📍 تفاصيل الخطأ:")
                traceback.print_exc()
            
            # محاولة معالجة عدد الأصناف
            try:
                items_count = trans['items_count']
                print(f"  🔍 معالجة عدد الأصناف: {repr(items_count)}")
                
                # فحص البيانات المعطوبة
                if items_count and isinstance(items_count, bytes):
                    print(f"    ❌ بيانات ثنائية في العدد: {repr(items_count)}")
                    try:
                        items_count = items_count.decode('utf-8')
                        print(f"    🔄 بعد فك التشفير: {repr(items_count)}")
                    except:
                        items_count = 0
                        print(f"    ❌ فشل فك التشفير، استخدام 0")
                
                # فحص النص المعطوب
                if items_count and isinstance(items_count, str):
                    if items_count.startswith("b'") or items_count.startswith('b"'):
                        print(f"    ❌ نص يحتوي على بيانات ثنائية: {repr(items_count)}")
                        items_count = 0
                
                if items_count:
                    count_int = int(float(items_count))
                    print(f"    ✅ تم تحويل العدد: {count_int}")
                else:
                    print(f"    ⚠️ عدد فارغ، استخدام 0")
            
            except Exception as e:
                print(f"    ❌ خطأ في معالجة العدد: {e}")
                print(f"    📍 تفاصيل الخطأ:")
                traceback.print_exc()
                
                # هذا هو المكان المحتمل للخطأ!
                if "invalid literal for int" in str(e) and "b'" in str(e):
                    print(f"    🎯 تم العثور على مصدر الخطأ!")
                    print(f"    📊 القيمة المعطوبة: {repr(items_count)}")
    
    except Exception as e:
        print(f"❌ خطأ عام في اختبار لوحة التحكم: {e}")
        print(f"📍 تفاصيل الخطأ:")
        traceback.print_exc()

def test_get_real_activities():
    """اختبار دالة get_real_activities مباشرة"""
    print("\n🧪 اختبار دالة get_real_activities...")
    
    try:
        # محاولة استيراد وتشغيل الدالة
        import sys
        sys.path.append('ui')
        
        # استيراد الدالة من main_window
        from ui.main_window import MainWindow
        
        # إنشاء نسخة وهمية من MainWindow
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء MainWindow
        main_window = MainWindow(root, None)
        
        # اختبار get_real_activities
        print("🔄 تشغيل get_real_activities...")
        activities = main_window.get_real_activities()
        print(f"✅ تم تحميل {len(activities)} نشاط")
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار get_real_activities: {e}")
        print(f"📍 تفاصيل الخطأ:")
        traceback.print_exc()
        
        # البحث عن الخطأ المحدد
        if "invalid literal for int" in str(e) and "b'" in str(e):
            print(f"    🎯 تم العثور على مصدر الخطأ في get_real_activities!")

if __name__ == "__main__":
    print("🚀 البحث المركز عن مصدر خطأ int() مع البيانات المعطوبة")
    print("=" * 60)
    
    # اختبار تحميل لوحة التحكم
    test_dashboard_directly()
    
    # اختبار get_real_activities
    test_get_real_activities()
    
    print("\n" + "=" * 60)
    print("✅ انتهى البحث المركز")
