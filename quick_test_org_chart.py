#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للجدول التنظيمي
Quick Test for Organizational Chart

اختبار سريع للوظائف الأساسية:
1. فحص قاعدة البيانات
2. اختبار الاستيراد
3. اختبار الحذف
"""

import sys
import os
from pathlib import Path
import pandas as pd

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from database import db_manager
    from models import OrganizationalChart
    print("✅ تم استيراد الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def quick_database_check():
    """فحص سريع لقاعدة البيانات"""
    print("🔍 فحص سريع لقاعدة البيانات...")
    
    try:
        # فحص الاتصال
        result = db_manager.fetch_one("SELECT 1")
        print("✅ الاتصال بقاعدة البيانات يعمل")
        
        # فحص الجدول
        try:
            count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            active_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            print(f"📊 إجمالي العناصر: {count}")
            print(f"📊 العناصر النشطة: {active_count}")
            print(f"📊 العناصر غير النشطة: {count - active_count}")
        except Exception as e:
            print(f"⚠️ مشكلة في الجدول: {e}")
            print("🔧 محاولة إنشاء الجدول...")
            OrganizationalChart.create_table()
            print("✅ تم إنشاء الجدول")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def quick_import_test():
    """اختبار سريع للاستيراد"""
    print("\n📥 اختبار سريع للاستيراد...")
    
    try:
        # إنشاء ملف Excel بسيط
        test_data = [
            {
                'اسم الصنف': 'اختبار سريع 1',
                'رقم الصنف': 'QUICK001',
                'اسم المعدة': 'قطعة',
                'الكمية': 1
            },
            {
                'اسم الصنف': 'اختبار سريع 2',
                'رقم الصنف': 'QUICK002',
                'اسم المعدة': 'كيلو',
                'الكمية': 2
            }
        ]
        
        df = pd.DataFrame(test_data)
        excel_file = project_root / "quick_test.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')
        print(f"✅ تم إنشاء ملف الاختبار: {excel_file}")
        
        # اختبار الاستيراد
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        
        result = import_organizational_chart_from_excel(str(excel_file))
        
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        
        print(f"📊 قبل الاستيراد: {before_count}")
        print(f"📊 بعد الاستيراد: {after_count}")
        print(f"📊 تم استيراد: {result.success_count}")
        print(f"📊 مكرر: {result.duplicate_count}")
        print(f"📊 أخطاء: {result.error_count}")
        
        # تنظيف الملف
        excel_file.unlink()
        print("🗑️ تم حذف ملف الاختبار")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_delete_test():
    """اختبار سريع للحذف"""
    print("\n🗑️ اختبار سريع للحذف...")
    
    try:
        before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        before_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        
        print(f"📊 قبل الحذف - نشط: {before_active}, إجمالي: {before_total}")
        
        # اختبار حذف العناصر التي تبدأ بـ QUICK
        quick_items = db_manager.fetch_all(
            "SELECT id FROM organizational_chart WHERE item_code LIKE 'QUICK%' AND is_active = 1"
        )
        
        deleted_count = 0
        for item_row in quick_items:
            item = OrganizationalChart.get_by_id(item_row[0])
            if item and item.delete():
                deleted_count += 1
        
        after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        after_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        
        print(f"📊 بعد الحذف - نشط: {after_active}, إجمالي: {after_total}")
        print(f"📊 تم حذف: {deleted_count} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحذف: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_delete_all_test():
    """اختبار سريع لحذف جميع الأصناف"""
    print("\n🗑️ اختبار سريع لحذف جميع الأصناف...")
    
    try:
        before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        
        if before_active == 0:
            print("📊 لا توجد عناصر نشطة للحذف")
            return True
        
        print(f"📊 عدد العناصر النشطة قبل الحذف: {before_active}")
        
        # تشغيل دالة حذف جميع الأصناف
        result = OrganizationalChart.delete_all()
        
        after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        
        print(f"📊 عدد العناصر النشطة بعد الحذف: {after_active}")
        print(f"📊 نتيجة الحذف: {result}")
        print(f"📊 تم حذف: {before_active - after_active} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حذف جميع الأصناف: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع للجدول التنظيمي")
    print("=" * 40)
    
    tests = [
        ("فحص قاعدة البيانات", quick_database_check),
        ("اختبار الاستيراد", quick_import_test),
        ("اختبار الحذف", quick_delete_test),
        ("اختبار حذف جميع الأصناف", quick_delete_all_test)
    ]
    
    successful = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            if test_func():
                successful += 1
                print(f"✅ {test_name} - نجح")
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 النتائج: {successful}/{total} اختبار نجح")
    
    if successful == total:
        print("🎉 جميع الاختبارات نجحت!")
    elif successful >= total * 0.7:
        print("⚠️ معظم الاختبارات نجحت")
    else:
        print("❌ عدة اختبارات فشلت")
    
    return 0 if successful >= total * 0.7 else 1

if __name__ == "__main__":
    sys.exit(main())