#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعادة إنشاء db_manager مع الإعدادات الجديدة
"""

def test_with_recreated_db_manager():
    """اختبار مع إعادة إنشاء db_manager"""
    print("🧪 اختبار مع إعادة إنشاء db_manager...")
    
    try:
        # استيراد الوحدة
        from database import recreate_db_manager
        
        # إعادة إنشاء db_manager مع الإعدادات الجديدة
        print("🔄 إعادة إنشاء db_manager...")
        new_db_manager = recreate_db_manager()
        
        print("✅ تم إعادة إنشاء db_manager بنجاح")
        
        # اختبار الاستعلام الذي كان يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        transactions = new_db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح!")
        
        # فحص كل معاملة
        for i, trans in enumerate(transactions):
            print(f"\nمعاملة {i+1}:")
            print(f"  التاريخ: {repr(trans['transaction_date'])}")
            print(f"  الرقم: {repr(trans['transaction_number'])}")
            print(f"  المستفيد: {repr(trans['beneficiary_name'])}")
            print(f"  عدد الأصناف: {repr(trans['items_count'])}")
        
        # اختبار إحصائيات لوحة التحكم
        print("\n🔄 اختبار إحصائيات لوحة التحكم...")
        
        # إجمالي أصناف الجدول التنظيمي
        total_items = new_db_manager.fetch_one("SELECT COUNT(*) as count FROM organizational_chart WHERE is_active = 1")
        total_items_count = total_items["count"] if total_items else 0
        print(f"✅ إجمالي أصناف الجدول التنظيمي: {total_items_count}")
        
        # إجمالي المستفيدين
        total_beneficiaries = new_db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1")
        total_beneficiaries_count = total_beneficiaries["count"] if total_beneficiaries else 0
        print(f"✅ إجمالي المستفيدين: {total_beneficiaries_count}")
        
        # إجمالي عمليات الصرف
        total_transactions = new_db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'مكتملة'")
        total_transactions_count = total_transactions["count"] if total_transactions else 0
        print(f"✅ إجمالي عمليات الصرف: {total_transactions_count}")
        
        # الأصناف منخفضة المخزون
        low_stock_items = new_db_manager.fetch_all("""
            SELECT ai.item_name, ai.current_quantity, ai.minimum_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= ai.minimum_quantity
            AND ai.minimum_quantity > 0
        """)
        low_stock_count = len(low_stock_items)
        print(f"✅ الأصناف منخفضة المخزون: {low_stock_count}")
        
        # الأصناف التي تحتاج تموين (كمية 5 أو أقل)
        supply_needed_items = new_db_manager.fetch_all("""
            SELECT ai.item_name, ai.current_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= 5
        """)
        supply_needed_count = len(supply_needed_items)
        print(f"✅ الأصناف التي تحتاج تموين: {supply_needed_count}")
        
        print(f"\n📊 ملخص إحصائيات لوحة التحكم:")
        print(f"  - إجمالي أصناف الجدول التنظيمي: {total_items_count}")
        print(f"  - إجمالي المستفيدين: {total_beneficiaries_count}")
        print(f"  - إجمالي عمليات الصرف: {total_transactions_count}")
        print(f"  - الأصناف منخفضة المخزون: {low_stock_count}")
        print(f"  - الأصناف التي تحتاج تموين: {supply_needed_count}")
        
        # التحقق من أن جميع البيانات موجودة
        if (total_items_count > 0 and total_beneficiaries_count > 0 and 
            total_transactions_count > 0):
            print("\n🎉 جميع بيانات لوحة التحكم متوفرة وتعمل بشكل صحيح!")
            return True
        else:
            print("\n⚠️ بعض بيانات لوحة التحكم مفقودة")
            return False
    
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_real_activities_fixed():
    """اختبار دالة get_real_activities مع db_manager الجديد"""
    print("\n🧪 اختبار دالة get_real_activities مع db_manager الجديد...")
    
    try:
        from database import db_manager
        from datetime import datetime
        
        # تنفيذ نفس الاستعلام الموجود في get_real_activities
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام الأنشطة...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة")
        
        activities = []
        
        for trans in transactions:
            try:
                date_str = trans['transaction_date']
                
                # معالجة البيانات المعطوبة (نفس الكود المحدث في main_window.py)
                if date_str and isinstance(date_str, bytes):
                    try:
                        date_str = date_str.decode('utf-8')
                    except:
                        date_str = None
                
                if date_str and isinstance(date_str, str):
                    if date_str.startswith("b'") or date_str.startswith('b"'):
                        date_str = None
                    elif not date_str.replace('-', '').replace(':', '').replace(' ', '').replace('.', '').isdigit():
                        date_str = None
                
                if isinstance(date_str, str) and date_str:
                    try:
                        if '.' in date_str:
                            date_str = date_str.split('.')[0]
                        
                        if ' ' in date_str:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        
                        time_str = trans_date.strftime('%H:%M')
                        date_display = trans_date.strftime('%m/%d')
                    except (ValueError, TypeError) as e:
                        print(f"خطأ في معالجة التاريخ: {e} - التاريخ: {repr(date_str)}")
                        time_str = "00:00"
                        date_display = "اليوم"
                else:
                    time_str = "00:00"
                    date_display = "اليوم"

                beneficiary = trans['beneficiary_name'] or "غير محدد"
                try:
                    items_count = int(float(trans['items_count'])) if trans['items_count'] else 0
                except (ValueError, TypeError):
                    items_count = 0
                
                activity = {
                    'type': 'transaction',
                    'description': f"عملية صرف - {beneficiary}",
                    'details': f"{items_count} صنف",
                    'time': time_str,
                    'date': date_display
                }
                
                activities.append(activity)
                print(f"  ✅ نشاط: {activity['description']} - {activity['time']}")
            
            except Exception as e:
                print(f"  ❌ خطأ في معالجة المعاملة: {e}")
        
        print(f"✅ تم إنشاء {len(activities)} نشاط بنجاح")
        return activities
    
    except Exception as e:
        print(f"❌ خطأ في اختبار get_real_activities: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    print("🚀 اختبار إعادة إنشاء db_manager مع الإعدادات الجديدة")
    print("=" * 60)
    
    # اختبار مع إعادة إنشاء db_manager
    dashboard_success = test_with_recreated_db_manager()
    
    # اختبار get_real_activities
    activities = test_get_real_activities_fixed()
    
    print("\n" + "=" * 60)
    if dashboard_success and len(activities) >= 0:
        print("✅ تم إصلاح مشكلة لوحة التحكم بنجاح!")
        print("🎉 يمكن الآن تشغيل التطبيق بدون أخطاء")
        print("📝 يجب تحديث ui/main_window.py لاستخدام recreate_db_manager()")
    else:
        print("❌ ما زالت هناك مشاكل تحتاج إصلاح")
    
    print("✅ انتهى الاختبار")
