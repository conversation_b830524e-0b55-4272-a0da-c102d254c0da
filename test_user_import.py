#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة اختبار الاستيراد للمستخدم
User Import Test Tool
"""

import os
import sys
import tempfile
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_excel():
    """إنشاء ملف Excel نموذجي للاختبار"""
    print("📝 إنشاء ملف Excel نموذجي...")
    
    # بيانات نموذجية مع أرقام مكررة لاختبار التجنب
    sample_data = [
        {'الاسم': 'أحمد محمد علي', 'الرقم العام': 'USER_TEST001', 'الرتبة': 'جندي', 'الإدارة': 'الإدارة العامة', 'الوحدة': 'الوحدة الأولى'},
        {'الاسم': 'محمد أحمد سالم', 'الرقم العام': 'USER_TEST002', 'الرتبة': 'عريف', 'الإدارة': 'إدارة الموارد', 'الوحدة': 'الوحدة الثانية'},
        {'الاسم': 'سالم علي محمد', 'الرقم العام': 'USER_TEST003', 'الرتبة': 'رقيب', 'الإدارة': 'الإدارة المالية', 'الوحدة': 'الوحدة الثالثة'},
        {'الاسم': 'علي سالم أحمد', 'الرقم العام': 'USER_TEST001', 'الرتبة': 'ملازم', 'الإدارة': 'الإدارة العامة', 'الوحدة': 'الوحدة الأولى'},  # رقم مكرر
        {'الاسم': 'خالد محمد علي', 'الرقم العام': 'USER_TEST004', 'الرتبة': 'نقيب', 'الإدارة': 'إدارة التدريب', 'الوحدة': 'الوحدة الرابعة'},
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data)
    
    # حفظ في ملف مؤقت
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    temp_path = temp_file.name
    temp_file.close()
    
    df.to_excel(temp_path, index=False)
    
    print(f"✅ تم إنشاء ملف Excel نموذجي: {temp_path}")
    print(f"📊 عدد الصفوف: {len(sample_data)}")
    print(f"🔄 يحتوي على رقم مكرر واحد لاختبار تجنب التكرار")
    
    return temp_path

def test_import_with_file(file_path):
    """اختبار الاستيراد مع ملف محدد"""
    print(f"🧪 اختبار الاستيراد مع الملف: {file_path}")
    
    try:
        from database import db_manager
        from utils.excel_import_manager import ExcelImportManager
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'USER_TEST%'")
        
        # عد المستفيدين قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 إجمالي المستفيدين النشطين قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        print("🔄 بدء عملية الاستيراد...")
        result = ExcelImportManager.import_beneficiaries_from_excel(
            file_path,
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        # عرض النتائج التفصيلية
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        print(f"   📝 إجمالي: {result.success_count + result.duplicate_count + result.error_count}")
        
        # عد المستفيدين بعد الاستيراد
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 إجمالي المستفيدين النشطين بعد الاستيراد: {after_count}")
        print(f"📈 الزيادة: {after_count - before_count} مستفيد")
        
        # عرض المستفيدين المستوردين
        if result.success_count > 0:
            imported_beneficiaries = db_manager.fetch_all(
                "SELECT name, number, rank FROM beneficiaries WHERE number LIKE 'USER_TEST%' ORDER BY number"
            )
            
            print(f"\n👥 المستفيدين المستوردين ({len(imported_beneficiaries)}):")
            for i, ben in enumerate(imported_beneficiaries, 1):
                print(f"   {i}. {ben[0]} - {ben[1]} - {ben[2]}")
        
        # عرض الأخطاء إن وجدت
        if result.error_count > 0:
            print(f"\n❌ الأخطاء ({result.error_count}):")
            for error in result.errors[:5]:  # أول 5 أخطاء
                print(f"   • {error}")
            if len(result.errors) > 5:
                print(f"   ... و {len(result.errors) - 5} أخطاء أخرى")
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'USER_TEST%'")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة اختبار الاستيراد للمستخدم")
    print("=" * 60)
    
    # إنشاء نافذة Tkinter مخفية لاختيار الملف
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # خيارات للمستخدم
    choice = messagebox.askyesnocancel(
        "اختبار الاستيراد",
        "اختر طريقة الاختبار:\n\n"
        "نعم: استخدام ملف Excel موجود\n"
        "لا: إنشاء ملف Excel نموذجي للاختبار\n"
        "إلغاء: إنهاء البرنامج"
    )
    
    if choice is None:  # إلغاء
        print("❌ تم إلغاء العملية")
        root.destroy()
        return
    
    file_path = None
    
    if choice:  # نعم - استخدام ملف موجود
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel للاستيراد",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        
        if not file_path:
            print("❌ لم يتم اختيار ملف")
            root.destroy()
            return
            
    else:  # لا - إنشاء ملف نموذجي
        file_path = create_sample_excel()
    
    # تنفيذ الاختبار
    print(f"\n🔄 اختبار الاستيراد مع الملف:")
    print(f"📁 {file_path}")
    print("-" * 60)
    
    success = test_import_with_file(file_path)
    
    # تنظيف الملف النموذجي إذا تم إنشاؤه
    if not choice and file_path and os.path.exists(file_path):
        os.unlink(file_path)
        print(f"🗑️ تم حذف الملف النموذجي")
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if success:
        print("✅ الاستيراد يعمل بشكل صحيح!")
        print("🎉 تم استيراد البيانات بنجاح وتجنب التكرار")
        messagebox.showinfo("نجح الاختبار", "✅ الاستيراد يعمل بشكل صحيح!\n\nتم استيراد البيانات بنجاح وتجنب التكرار")
    else:
        print("❌ هناك مشكلة في الاستيراد")
        print("🔧 راجع الأخطاء أعلاه لمعرفة السبب")
        messagebox.showerror("فشل الاختبار", "❌ هناك مشكلة في الاستيراد\n\nراجع وحدة التحكم لمعرفة التفاصيل")
    
    print("=" * 60)
    root.destroy()

if __name__ == "__main__":
    main()
