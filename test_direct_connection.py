#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال المباشر بقاعدة البيانات بدون detect_types
"""

import sqlite3

def test_direct_connection():
    """اختبار الاتصال المباشر"""
    print("🧪 اختبار الاتصال المباشر بقاعدة البيانات...")
    
    try:
        # الاتصال المباشر بدون detect_types
        conn = sqlite3.connect('stores_management.db', detect_types=0)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("✅ تم الاتصال المباشر بنجاح (detect_types=0)")
        
        # اختبار الاستعلام الذي كان يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        cursor.execute(transactions_query)
        transactions = cursor.fetchall()
        
        print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح!")
        
        # فحص كل معاملة
        for i, trans in enumerate(transactions):
            print(f"\nمعاملة {i+1}:")
            print(f"  التاريخ: {repr(trans['transaction_date'])} (نوع: {type(trans['transaction_date'])})")
            print(f"  الرقم: {repr(trans['transaction_number'])}")
            print(f"  المستفيد: {repr(trans['beneficiary_name'])}")
            print(f"  عدد الأصناف: {repr(trans['items_count'])} (نوع: {type(trans['items_count'])})")
        
        # اختبار إحصائيات لوحة التحكم
        print("\n🔄 اختبار إحصائيات لوحة التحكم...")
        
        # إجمالي أصناف الجدول التنظيمي
        cursor.execute("SELECT COUNT(*) as count FROM organizational_chart WHERE is_active = 1")
        total_items = cursor.fetchone()
        total_items_count = total_items["count"] if total_items else 0
        print(f"✅ إجمالي أصناف الجدول التنظيمي: {total_items_count}")
        
        # إجمالي المستفيدين
        cursor.execute("SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1")
        total_beneficiaries = cursor.fetchone()
        total_beneficiaries_count = total_beneficiaries["count"] if total_beneficiaries else 0
        print(f"✅ إجمالي المستفيدين: {total_beneficiaries_count}")
        
        # إجمالي عمليات الصرف
        cursor.execute("SELECT COUNT(*) as count FROM transactions WHERE status = 'مكتملة'")
        total_transactions = cursor.fetchone()
        total_transactions_count = total_transactions["count"] if total_transactions else 0
        print(f"✅ إجمالي عمليات الصرف: {total_transactions_count}")
        
        # الأصناف منخفضة المخزون
        cursor.execute("""
            SELECT ai.item_name, ai.current_quantity, ai.minimum_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= ai.minimum_quantity
            AND ai.minimum_quantity > 0
        """)
        low_stock_items = cursor.fetchall()
        low_stock_count = len(low_stock_items)
        print(f"✅ الأصناف منخفضة المخزون: {low_stock_count}")
        
        # الأصناف التي تحتاج تموين (كمية 5 أو أقل)
        cursor.execute("""
            SELECT ai.item_name, ai.current_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= 5
        """)
        supply_needed_items = cursor.fetchall()
        supply_needed_count = len(supply_needed_items)
        print(f"✅ الأصناف التي تحتاج تموين: {supply_needed_count}")
        
        conn.close()
        
        print(f"\n📊 ملخص إحصائيات لوحة التحكم:")
        print(f"  - إجمالي أصناف الجدول التنظيمي: {total_items_count}")
        print(f"  - إجمالي المستفيدين: {total_beneficiaries_count}")
        print(f"  - إجمالي عمليات الصرف: {total_transactions_count}")
        print(f"  - الأصناف منخفضة المخزون: {low_stock_count}")
        print(f"  - الأصناف التي تحتاج تموين: {supply_needed_count}")
        
        # التحقق من أن جميع البيانات موجودة
        if (total_items_count > 0 and total_beneficiaries_count > 0 and 
            total_transactions_count > 0):
            print("\n🎉 جميع بيانات لوحة التحكم متوفرة وتعمل بشكل صحيح!")
            return True
        else:
            print("\n⚠️ بعض بيانات لوحة التحكم مفقودة")
            return False
    
    except Exception as e:
        print(f"❌ خطأ في الاختبار المباشر: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_detect_types():
    """اختبار مع detect_types لمقارنة النتائج"""
    print("\n🧪 اختبار مع detect_types (للمقارنة)...")
    
    try:
        # الاتصال مع detect_types (الإعدادات القديمة)
        conn = sqlite3.connect('stores_management.db', 
                              detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("✅ تم الاتصال مع detect_types")
        
        # اختبار الاستعلام الذي كان يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات مع detect_types...")
        cursor.execute(transactions_query)
        transactions = cursor.fetchall()
        
        print(f"✅ تم تحميل {len(transactions)} معاملة مع detect_types")
        
        conn.close()
        return True
    
    except Exception as e:
        print(f"❌ خطأ متوقع مع detect_types: {e}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار الاتصال المباشر بقاعدة البيانات")
    print("=" * 60)
    
    # اختبار الاتصال المباشر بدون detect_types
    direct_success = test_direct_connection()
    
    # اختبار مع detect_types للمقارنة
    detect_types_success = test_with_detect_types()
    
    print("\n" + "=" * 60)
    if direct_success and not detect_types_success:
        print("✅ تأكيد: المشكلة في detect_types!")
        print("🔧 الحل: تحديث DatabaseManager لاستخدام detect_types=0")
    elif direct_success and detect_types_success:
        print("⚠️ كلا الطريقتين تعملان - قد تكون المشكلة في مكان آخر")
    else:
        print("❌ ما زالت هناك مشاكل تحتاج إصلاح")
    
    print("✅ انتهى الاختبار")
