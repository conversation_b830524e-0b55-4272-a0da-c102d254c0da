#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة الجدول التنظيمي الفعلية
Real UI Test for Organizational Chart Window

هذا الاختبار يفتح الشاشة الفعلية ويختبر:
1. فتح شاشة الجدول التنظيمي
2. اختبار عملية الاستيراد من Excel
3. اختبار عملية حذف جميع الأصناف
4. التفاعل مع الواجهة الحقيقية
"""

import sys
import os
from pathlib import Path
import tkinter as tk
import ttkbootstrap as ttk_bs
import pandas as pd
import time
import threading
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from database import db_manager
    from models import OrganizationalChart
    from ui.organizational_chart_window import OrganizationalChartWindow
    from utils.organizational_chart_import import import_organizational_chart_from_excel
    print("✅ تم استيراد جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class RealUITester:
    """فئة اختبار الواجهة الحقيقية"""
    
    def __init__(self):
        self.main_window = None
        self.org_chart_window = None
        self.test_excel_file = None
        self.test_results = []
        
        print("🔧 بدء اختبار الواجهة الحقيقية لشاشة الجدول التنظيمي")
        print("=" * 60)
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': timestamp
        }
        self.test_results.append(result)
        
        print(f"{status} [{timestamp}] {test_name}")
        if message:
            print(f"    📝 {message}")
        print()
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        try:
            print("🖼️ إنشاء النافذة الرئيسية...")
            
            self.main_window = ttk_bs.Window(
                title="اختبار شاشة الجدول التنظيمي - الواجهة الحقيقية",
                themename="cosmo",
                size=(1400, 900),
                position=(100, 50)
            )
            
            # إضافة عنوان
            title_label = ttk_bs.Label(
                self.main_window,
                text="اختبار شاشة الجدول التنظيمي",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            )
            title_label.pack(pady=20)
            
            # إضافة أزرار الاختبار
            button_frame = ttk_bs.Frame(self.main_window)
            button_frame.pack(pady=20)
            
            # زر فتح شاشة الجدول التنظيمي
            open_btn = ttk_bs.Button(
                button_frame,
                text="فتح شاشة الجدول التنظيمي",
                command=self.open_org_chart_window,
                bootstyle="success",
                width=25
            )
            open_btn.pack(side=tk.LEFT, padx=10)
            
            # زر إنشاء ملف Excel للاختبار
            create_excel_btn = ttk_bs.Button(
                button_frame,
                text="إنشاء ملف Excel للاختبار",
                command=self.create_test_excel,
                bootstyle="info",
                width=25
            )
            create_excel_btn.pack(side=tk.LEFT, padx=10)
            
            # زر اختبار الاستيراد
            import_btn = ttk_bs.Button(
                button_frame,
                text="اختبار الاستيراد",
                command=self.test_import_function,
                bootstyle="warning",
                width=25
            )
            import_btn.pack(side=tk.LEFT, padx=10)
            
            # زر اختبار الحذف
            delete_btn = ttk_bs.Button(
                button_frame,
                text="اختبار حذف جميع الأصناف",
                command=self.test_delete_all_function,
                bootstyle="danger",
                width=25
            )
            delete_btn.pack(side=tk.LEFT, padx=10)
            
            # منطقة عرض النتائج
            results_frame = ttk_bs.LabelFrame(
                self.main_window,
                text="نتائج الاختبار",
                padding=10
            )
            results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # صندوق النص لعرض النتائج
            self.results_text = tk.Text(
                results_frame,
                wrap=tk.WORD,
                font=("Consolas", 10),
                bg="#f8f9fa",
                fg="#212529"
            )
            
            # شريط التمرير
            scrollbar = ttk_bs.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
            self.results_text.configure(yscrollcommand=scrollbar.set)
            
            self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # إضافة رسالة ترحيب
            welcome_msg = """
🎯 مرحباً بك في اختبار شاشة الجدول التنظيمي

📋 خطوات الاختبار:
1. اضغط على "فتح شاشة الجدول التنظيمي" لفتح الشاشة
2. اضغط على "إنشاء ملف Excel للاختبار" لإنشاء بيانات اختبار
3. اضغط على "اختبار الاستيراد" لاختبار استيراد البيانات
4. اضغط على "اختبار حذف جميع الأصناف" لاختبار الحذف

⚠️ تأكد من أن شاشة الجدول التنظيمي مفتوحة قبل تشغيل الاختبارات
            """
            
            self.add_to_results(welcome_msg)
            
            self.log_result("إنشاء النافذة الرئيسية", True, "تم إنشاء الواجهة بنجاح")
            return True
            
        except Exception as e:
            self.log_result("إنشاء النافذة الرئيسية", False, f"خطأ: {e}")
            return False
    
    def add_to_results(self, message: str):
        """إضافة رسالة إلى منطقة النتائج"""
        if hasattr(self, 'results_text'):
            self.results_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
            self.results_text.see(tk.END)
            self.main_window.update()
    
    def open_org_chart_window(self):
        """فتح شاشة الجدول التنظيمي"""
        try:
            self.add_to_results("🖼️ محاولة فتح شاشة الجدول التنظيمي...")
            
            # التحقق من وجود بيانات في قاعدة البيانات
            count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            self.add_to_results(f"📊 عدد العناصر النشطة في قاعدة البيانات: {count}")
            
            # إنشاء شاشة الجدول التنظيمي
            self.org_chart_window = OrganizationalChartWindow(self.main_window, None)
            
            # التحقق من إنشاء الشاشة
            if hasattr(self.org_chart_window, 'window') and self.org_chart_window.window:
                self.add_to_results("✅ تم إنشاء شاشة الجدول التنظيمي بنجاح")
                
                # التحقق من تحميل البيانات
                if hasattr(self.org_chart_window, 'tree'):
                    tree_items = len(self.org_chart_window.tree.get_children())
                    self.add_to_results(f"📊 عدد العناصر في الجدول: {tree_items}")
                
                # وضع النافذة في المقدمة
                self.org_chart_window.window.lift()
                self.org_chart_window.window.focus_force()
                
                self.log_result("فتح شاشة الجدول التنظيمي", True, "الشاشة مفتوحة ومتاحة للاستخدام")
            else:
                raise Exception("فشل في إنشاء نافذة الجدول التنظيمي")
                
        except Exception as e:
            self.add_to_results(f"❌ خطأ في فتح الشاشة: {e}")
            self.log_result("فتح شاشة الجدول التنظيمي", False, f"خطأ: {e}")
            import traceback
            self.add_to_results(f"تفاصيل الخطأ: {traceback.format_exc()}")
    
    def create_test_excel(self):
        """إنشاء ملف Excel للاختبار"""
        try:
            self.add_to_results("📄 إنشاء ملف Excel للاختبار...")
            
            # بيانات الاختبار
            test_data = [
                {
                    'التسلسل': 1,
                    'رقم الصنف': 'UI_TEST001',
                    'اسم الصنف': 'صنف اختبار واجهة 1',
                    'اسم المعدة': 'قطعة',
                    'الكمية': 10,
                    'الملاحظات': 'اختبار واجهة المستخدم'
                },
                {
                    'التسلسل': 2,
                    'رقم الصنف': 'UI_TEST002',
                    'اسم الصنف': 'صنف اختبار واجهة 2',
                    'اسم المعدة': 'كيلو',
                    'الكمية': 25,
                    'الملاحظات': 'اختبار واجهة المستخدم'
                },
                {
                    'التسلسل': 3,
                    'رقم الصنف': 'UI_TEST003',
                    'اسم الصنف': 'صنف اختبار واجهة 3',
                    'اسم المعدة': 'متر',
                    'الكمية': 15,
                    'الملاحظات': 'اختبار واجهة المستخدم'
                },
                {
                    'التسلسل': 4,
                    'رقم الصنف': '',  # بدون رقم صنف لاختبار معالجة الأخطاء
                    'اسم الصنف': 'صنف بدون رقم',
                    'اسم المعدة': 'قطعة',
                    'الكمية': 5,
                    'الملاحظات': 'اختبار بدون رقم صنف'
                },
                {
                    'التسلسل': 5,
                    'رقم الصنف': 'UI_TEST001',  # رقم مكرر
                    'اسم الصنف': 'صنف مكرر',
                    'اسم المعدة': 'قطعة',
                    'الكمية': 8,
                    'الملاحظات': 'اختبار رقم مكرر'
                }
            ]
            
            # إنشاء DataFrame وحفظ الملف
            df = pd.DataFrame(test_data)
            self.test_excel_file = project_root / "ui_test_org_chart.xlsx"
            df.to_excel(self.test_excel_file, index=False, engine='openpyxl')
            
            self.add_to_results(f"✅ تم إنشاء ملف Excel: {self.test_excel_file}")
            self.add_to_results(f"📊 عدد الصفوف: {len(test_data)}")
            
            self.log_result("إنشاء ملف Excel", True, f"الملف محفوظ في: {self.test_excel_file}")
            
        except Exception as e:
            self.add_to_results(f"❌ خطأ في إنشاء ملف Excel: {e}")
            self.log_result("إنشاء ملف Excel", False, f"خطأ: {e}")
    
    def test_import_function(self):
        """اختبار وظيفة الاستيراد"""
        try:
            self.add_to_results("📥 بدء اختبار الاستيراد...")
            
            # التحقق من وجود ملف Excel
            if not self.test_excel_file or not self.test_excel_file.exists():
                self.add_to_results("⚠️ ملف Excel غير موجود، سيتم إنشاؤه أولاً...")
                self.create_test_excel()
                time.sleep(1)
            
            # التحقق من وجود شاشة الجدول التنظيمي
            if not self.org_chart_window:
                self.add_to_results("⚠️ شاشة الجدول التنظيمي غير مفتوحة، سيتم فتحها أولاً...")
                self.open_org_chart_window()
                time.sleep(2)
            
            # حالة قبل الاستيراد
            before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            self.add_to_results(f"📊 عدد العناصر قبل الاستيراد: {before_count}")
            
            # تشغيل دالة الاستيراد
            self.add_to_results("🔄 تشغيل دالة الاستيراد...")
            result = import_organizational_chart_from_excel(str(self.test_excel_file))
            
            # حالة بعد الاستيراد
            after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            self.add_to_results(f"📊 عدد العناصر بعد الاستيراد: {after_count}")
            
            # عرض النتائج
            self.add_to_results(f"📊 نتائج الاستيراد:")
            self.add_to_results(f"    ✅ نجح: {result.success_count}")
            self.add_to_results(f"    🔄 مكرر: {result.duplicate_count}")
            self.add_to_results(f"    ❌ خطأ: {result.error_count}")
            self.add_to_results(f"    ⏱️ وقت المعالجة: {result.processing_time:.2f} ثانية")
            
            # عرض الأخطاء إن وجدت
            if hasattr(result, 'errors') and result.errors:
                self.add_to_results("⚠️ أخطاء الاستيراد:")
                for error in result.errors[:3]:  # أول 3 أخطاء فقط
                    self.add_to_results(f"    - {error}")
            
            # تحديث الشاشة إذا كانت مفتوحة
            if self.org_chart_window and hasattr(self.org_chart_window, 'load_data'):
                self.add_to_results("🔄 تحديث بيانات الشاشة...")
                self.org_chart_window.load_data()
                
                # التحقق من تحديث الجدول
                if hasattr(self.org_chart_window, 'tree'):
                    tree_items = len(self.org_chart_window.tree.get_children())
                    self.add_to_results(f"📊 عدد العناصر في الجدول بعد التحديث: {tree_items}")
            
            success = result.success_count > 0 or result.duplicate_count > 0
            self.log_result("اختبار الاستيراد", success, 
                          f"تم استيراد {result.success_count} عنصر، {result.duplicate_count} مكرر، {result.error_count} خطأ")
            
        except Exception as e:
            self.add_to_results(f"❌ خطأ في اختبار الاستيراد: {e}")
            self.log_result("اختبار الاستيراد", False, f"خطأ: {e}")
            import traceback
            self.add_to_results(f"تفاصيل الخطأ: {traceback.format_exc()}")
    
    def test_delete_all_function(self):
        """اختبار وظيفة حذف جميع الأصناف"""
        try:
            self.add_to_results("🗑️ بدء اختبار حذف جميع الأصناف...")
            
            # التحقق من وجود شاشة الجدول التنظيمي
            if not self.org_chart_window:
                self.add_to_results("⚠️ شاشة الجدول التنظيمي غير مفتوحة، سيتم فتحها أولاً...")
                self.open_org_chart_window()
                time.sleep(2)
            
            # حالة قبل الحذف
            before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            before_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            self.add_to_results(f"📊 قبل الحذف:")
            self.add_to_results(f"    - عناصر نشطة: {before_active}")
            self.add_to_results(f"    - إجمالي العناصر: {before_total}")
            
            if before_active == 0:
                self.add_to_results("⚠️ لا توجد عناصر نشطة للحذف")
                self.log_result("اختبار حذف جميع الأصناف", True, "لا توجد عناصر للحذف")
                return
            
            # تأكيد من المستخدم
            self.add_to_results(f"⚠️ سيتم حذف {before_active} عنصر نشط")
            self.add_to_results("🔄 تشغيل دالة الحذف...")
            
            # تشغيل دالة الحذف
            delete_result = OrganizationalChart.delete_all()
            
            # حالة بعد الحذف
            after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            after_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            self.add_to_results(f"📊 بعد الحذف:")
            self.add_to_results(f"    - عناصر نشطة: {after_active}")
            self.add_to_results(f"    - إجمالي العناصر: {after_total}")
            self.add_to_results(f"    - تم حذف: {before_active - after_active} عنصر")
            self.add_to_results(f"    - نتيجة العملية: {delete_result}")
            
            # تحديث الشاشة إذا كانت مفتوحة
            if self.org_chart_window and hasattr(self.org_chart_window, 'load_data'):
                self.add_to_results("🔄 تحديث بيانات الشاشة...")
                self.org_chart_window.load_data()
                
                # التحقق من تحديث الجدول
                if hasattr(self.org_chart_window, 'tree'):
                    tree_items = len(self.org_chart_window.tree.get_children())
                    self.add_to_results(f"📊 عدد العناصر في الجدول بعد التحديث: {tree_items}")
            
            success = delete_result is not False
            deleted_count = before_active - after_active
            
            self.log_result("اختبار حذف جميع الأصناف", success, 
                          f"تم حذف {deleted_count} عنصر من أصل {before_active}")
            
        except Exception as e:
            self.add_to_results(f"❌ خطأ في اختبار الحذف: {e}")
            self.log_result("اختبار حذف جميع الأصناف", False, f"خطأ: {e}")
            import traceback
            self.add_to_results(f"تفاصيل الخطأ: {traceback.format_exc()}")
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            # حذف ملف Excel المؤقت
            if self.test_excel_file and self.test_excel_file.exists():
                self.test_excel_file.unlink()
                self.add_to_results(f"🗑️ تم حذف ملف Excel المؤقت")
            
            # حذف بيانات الاختبار من قاعدة البيانات
            deleted_count = db_manager.execute_query(
                "DELETE FROM organizational_chart WHERE item_code LIKE 'UI_TEST%'"
            ).rowcount
            
            if deleted_count > 0:
                self.add_to_results(f"🗑️ تم حذف {deleted_count} عنصر اختبار من قاعدة البيانات")
            
            self.log_result("تنظيف الموارد", True, f"تم تنظيف {deleted_count} عنصر")
            
        except Exception as e:
            self.add_to_results(f"⚠️ خطأ في التنظيف: {e}")
    
    def show_final_results(self):
        """عرض النتائج النهائية"""
        self.add_to_results("\n" + "=" * 50)
        self.add_to_results("📊 ملخص نتائج الاختبار")
        self.add_to_results("=" * 50)
        
        successful_tests = sum(1 for result in self.test_results if result['success'])
        total_tests = len(self.test_results)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.add_to_results(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        self.add_to_results("")
        
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            self.add_to_results(f"{status} [{result['timestamp']}] {result['test']}")
            if result['message']:
                self.add_to_results(f"    📝 {result['message']}")
        
        self.add_to_results("=" * 50)
        
        if success_rate >= 80:
            self.add_to_results("🎉 الاختبار ممتاز - النظام يعمل بشكل جيد!")
        elif success_rate >= 60:
            self.add_to_results("⚠️ الاختبار جيد - توجد بعض المشاكل البسيطة")
        else:
            self.add_to_results("❌ الاختبار ضعيف - توجد مشاكل تحتاج إلى إصلاح")
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        try:
            self.cleanup()
            self.show_final_results()
            
            # إغلاق شاشة الجدول التنظيمي إذا كانت مفتوحة
            if self.org_chart_window and hasattr(self.org_chart_window, 'window'):
                try:
                    self.org_chart_window.window.destroy()
                except:
                    pass
            
            # إغلاق النافذة الرئيسية
            if self.main_window:
                self.main_window.destroy()
                
        except Exception as e:
            print(f"خطأ في الإغلاق: {e}")
    
    def run(self):
        """تشغيل الاختبار"""
        if self.create_main_window():
            # ربط حدث الإغلاق
            self.main_window.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # تشغيل الحلقة الرئيسية
            self.main_window.mainloop()
        else:
            print("❌ فشل في إنشاء النافذة الرئيسية")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار الواجهة الحقيقية لشاشة الجدول التنظيمي")
    print("=" * 60)
    print("هذا الاختبار سيفتح واجهة تفاعلية لاختبار:")
    print("1. فتح شاشة الجدول التنظيمي")
    print("2. اختبار الاستيراد من Excel")
    print("3. اختبار حذف جميع الأصناف")
    print("4. التفاعل مع الواجهة الحقيقية")
    print("=" * 60)
    
    tester = RealUITester()
    tester.run()

if __name__ == "__main__":
    main()