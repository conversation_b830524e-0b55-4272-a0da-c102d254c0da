#!/usr/bin/env python3
"""
شاشة حالة المخزون
Inventory Status Window

ملاحظة: نوع العهدة يشير إلى تصنيف الأصناف:
- مستهلكة: أصناف تستهلك بالاستخدام (مثل الأوراق، الأقلام)
- مستديمة: أصناف تبقى لفترة طويلة (مثل الأجهزة، الأثاث)
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category
from database import db_manager

class InventoryStatusWindow:
    """شاشة حالة المخزون"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.status_window = None
        self.status_tree = None
        self.search_var = None
        self.filter_category_var = None
        self.filter_status_var = None

        # قاموس لحفظ معرفات الأصناف
        self.item_ids_map = {}

        self.setup_window()
        self.load_inventory_status()

        # تسجيل النافذة في النظام العام
        try:
            from ui.add_inventory_movement_window import register_inventory_window
            register_inventory_window(self)
        except Exception as e:
            print(f"⚠️ خطأ في تسجيل نافذة حالة المخزون: {e}")
    
    def setup_window(self):
        """إعداد النافذة"""
        self.status_window = tk.Toplevel(self.parent)
        self.status_window.title("📊 حالة المخزون")
        self.status_window.geometry("1200x700")
        self.status_window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.status_window.lift()
        self.status_window.focus_force()

        # ربط حدث إغلاق النافذة
        self.status_window.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.status_window.update_idletasks()
        
        screen_width = self.status_window.winfo_screenwidth()
        screen_height = self.status_window.winfo_screenheight()
        
        window_width = 1200
        window_height = 700
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.status_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.status_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters(main_frame)
        
        # جدول حالة المخزون
        self.create_status_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📊 حالة المخزون",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        # زر إضافة حركة مخزون
        add_movement_btn = ttk_bs.Button(
            tools_frame,
            text="➕ إضافة حركة",
            command=self.add_movement_for_selected,
            bootstyle="success",
            width=20
        )
        add_movement_btn.pack(side=RIGHT, padx=5)

        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_inventory_status,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)
        
        # زر تصدير
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=self.export_status,
            bootstyle="outline-success",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)
        
        # زر طباعة
        print_btn = ttk_bs.Button(
            tools_frame,
            text="🖨️ طباعة",
            command=self.print_status,
            bootstyle="outline-info",
            width=15
        )
        print_btn.pack(side=RIGHT, padx=5)
    
    def create_filters(self, parent):
        """إنشاء شريط البحث والفلاتر"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))

        # الصف الأول - البحث والفئة
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)

        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        # إزالة التطبيق التلقائي للفلتر عند الكتابة

        # نوع العهدة (الفئة)
        ttk_bs.Label(row1_frame, text="نوع العهدة:").pack(side=LEFT, padx=(0, 5))
        self.filter_category_var = tk.StringVar(value="الكل")
        category_combo = ttk_bs.Combobox(
            row1_frame,
            textvariable=self.filter_category_var,
            values=["الكل", "مستهلكة", "مستديمة"],
            state="readonly",
            width=20
        )
        category_combo.pack(side=LEFT, padx=(0, 20))
        # إزالة التطبيق التلقائي للفلتر

        # حالة المخزون
        ttk_bs.Label(row1_frame, text="حالة المخزون:").pack(side=LEFT, padx=(0, 5))
        self.filter_status_var = tk.StringVar(value="الكل")
        status_combo = ttk_bs.Combobox(
            row1_frame,
            textvariable=self.filter_status_var,
            values=["الكل", "متوفر", "منخفض", "نفد", "تحذير"],
            state="readonly",
            width=15
        )
        status_combo.pack(side=LEFT, padx=(0, 20))
        # إزالة التطبيق التلقائي للفلتر

        # الصف الثاني - أزرار التحكم
        row2_frame = ttk_bs.Frame(filters_frame)
        row2_frame.pack(fill=X, padx=10, pady=5)

        # زر تطبيق الفلاتر
        apply_btn = ttk_bs.Button(
            row2_frame,
            text="🔍 تطبيق الفلاتر",
            command=self.apply_filters,
            bootstyle="primary",
            width=20
        )
        apply_btn.pack(side=LEFT, padx=(0, 10))

        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row2_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=10)

        # زر تحديث البيانات
        refresh_btn = ttk_bs.Button(
            row2_frame,
            text="🔄 تحديث البيانات",
            command=self.load_inventory_status,
            bootstyle="outline-secondary",
            width=20
        )
        refresh_btn.pack(side=LEFT, padx=10)
    
    def create_status_table(self, parent):
        """إنشاء جدول حالة المخزون"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 حالة المخزون", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview مع الأعمدة الجديدة
        columns = ("sequence", "item_number", "item_name", "custody_type", "classification",
                  "entered_qty", "dispensed_qty", "current_qty", "unit", "actions")
        self.status_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة الجديدة
        headings = {
            "sequence": "التسلسل",
            "item_number": "رقم الصنف",
            "item_name": "اسم الصنف",
            "custody_type": "نوع العهدة",
            "classification": "التصنيف",
            "entered_qty": "الكمية المضافة",
            "dispensed_qty": "الكمية المصروفة",
            "current_qty": "الكمية الحالية",
            "unit": "وحدة الصرف",
            "actions": "الإجراءات"
        }
        
        for col, heading in headings.items():
            self.status_tree.heading(col, text=heading)
            self.status_tree.column(col, width=100, anchor="center")
        
        # تعيين عرض أعمدة محددة للأعمدة الجديدة
        self.status_tree.column("sequence", width=80)
        self.status_tree.column("item_number", width=120)
        self.status_tree.column("item_name", width=200)
        self.status_tree.column("custody_type", width=120)
        self.status_tree.column("classification", width=120)
        self.status_tree.column("entered_qty", width=100)
        self.status_tree.column("dispensed_qty", width=100)
        self.status_tree.column("current_qty", width=100)
        self.status_tree.column("unit", width=100)
        self.status_tree.column("actions", width=80)
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.status_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.status_tree.xview)
        self.status_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.status_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)
        scrollbar_x.pack(side=BOTTOM, fill=X, padx=10)
        
        # تنسيق الألوان للصفوف
        self.status_tree.tag_configure("out_of_stock", background="#ffebee", foreground="#c62828")  # أحمر فاتح للنفاد
        self.status_tree.tag_configure("low_stock", background="#fff3e0", foreground="#ef6c00")     # برتقالي فاتح للمنخفض
        self.status_tree.tag_configure("warning_stock", background="#fffde7", foreground="#f57f17") # أصفر فاتح للتحذير

        # ربط الأحداث
        self.status_tree.bind('<Double-1>', self.on_item_double_click)
        self.status_tree.bind('<Button-1>', self.on_item_click)
        self.status_tree.bind('<Button-3>', self.show_context_menu)  # النقر بالزر الأيمن
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)
        
        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)
        
        # إحصائيات سريعة
        self.stats_var = tk.StringVar(value="")
        stats_label = ttk_bs.Label(
            status_frame,
            textvariable=self.stats_var,
            bootstyle="info"
        )
        stats_label.pack(side=RIGHT)
    
    def load_categories(self, combo):
        """تحميل قائمة الفئات"""
        try:
            categories = Category.get_all() if hasattr(Category, 'get_all') else []
            cat_names = ["الكل"] + [cat.name for cat in categories]
            combo['values'] = cat_names
            combo.set("الكل")
        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")
            combo['values'] = ["الكل"]
            combo.set("الكل")
    
    def load_inventory_status(self):
        """تحميل حالة المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.status_tree.get_children():
                self.status_tree.delete(item)

            # مسح قاموس معرفات الأصناف
            self.item_ids_map.clear()

            self.status_var.set("جاري تحميل البيانات...")

            # تحميل البيانات من جدول الأصناف المضافة
            from models import AddedItem
            added_items = AddedItem.get_all()

            total_items = 0
            sequence = 1

            # إضافة البيانات للجدول
            for item in added_items:
                try:
                    # حساب الكميات من جدول حركات المخزون
                    entered_qty = self.get_entered_quantity(item.item_number)
                    dispensed_qty = self.get_dispensed_quantity(item.item_number)
                    current_qty = max(0, entered_qty - dispensed_qty)  # تأكد من عدم كون الكمية سالبة

                    # التأكد من أن جميع القيم صحيحة قبل الإدراج
                    values = (
                        sequence,
                        str(item.item_number) if item.item_number else "",
                        str(item.item_name) if item.item_name else "",
                        str(item.custody_type) if item.custody_type else "",
                        str(item.classification) if item.classification else "",
                        int(entered_qty) if entered_qty else 0,
                        int(dispensed_qty) if dispensed_qty else 0,
                        int(current_qty) if current_qty else 0,
                        str(item.unit) if item.unit else "",
                        "+"  # زر الإجراءات
                    )

                    # إدراج البيانات في الجدول مع معالجة الأخطاء
                    tree_item_id = self.status_tree.insert('', 'end', values=values, tags=(f"item_{item.id}",))

                    # حفظ معرف الصنف في القاموس
                    self.item_ids_map[tree_item_id] = {
                        'item_id': item.id,
                        'item_number': item.item_number,
                        'item_name': item.item_name
                    }

                    sequence += 1
                    total_items += 1

                except Exception as item_error:
                    print(f"⚠️ خطأ في إضافة الصنف {item.item_number}: {item_error}")
                    continue

            # تحديث شريط الحالة
            self.status_var.set(f"تم تحميل {total_items} صنف")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل حالة المخزون: {e}")
            self.status_var.set("خطأ في تحميل البيانات")

    def get_dispensed_quantity(self, item_number):
        """حساب الكمية المصروفة لصنف معين من جدول عمليات الصرف"""
        try:
            from database import db_manager

            # البحث في جدول عمليات الصرف عبر ربط الجداول
            # نحتاج للربط مع جدول added_items للحصول على item_number
            query_transactions = """
                SELECT SUM(ti.quantity) as total_dispensed
                FROM transaction_items ti
                INNER JOIN transactions t ON ti.transaction_id = t.id
                INNER JOIN added_items ai ON ti.item_id = ai.id
                WHERE ai.item_number = ?
            """

            result = db_manager.fetch_one(query_transactions, (item_number,))
            total_from_transactions = float(result['total_dispensed']) if result and result['total_dispensed'] else 0.0

            # البحث في جدول حركات المخزون كمصدر إضافي
            query_movements = """
                SELECT SUM(quantity) as total_dispensed
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف'
            """

            result_movements = db_manager.fetch_one(query_movements, (item_number,))
            total_from_movements = float(result_movements['total_dispensed']) if result_movements and result_movements['total_dispensed'] else 0.0

            # البحث في جدول items أيضاً (إذا كان يستخدم)
            query_items = """
                SELECT SUM(ti.quantity) as total_dispensed
                FROM transaction_items ti
                INNER JOIN transactions t ON ti.transaction_id = t.id
                INNER JOIN items i ON ti.item_id = i.id
                WHERE i.code = ?
            """

            result_items = db_manager.fetch_one(query_items, (item_number,))
            total_from_items = float(result_items['total_dispensed']) if result_items and result_items['total_dispensed'] else 0.0

            # استخدام أكبر قيمة من المصادر الثلاثة
            total_dispensed = max(total_from_transactions, total_from_movements, total_from_items)

            print(f"📊 الكمية المصروفة للصنف {item_number}:")
            print(f"   من عمليات الصرف (added_items): {total_from_transactions}")
            print(f"   من عمليات الصرف (items): {total_from_items}")
            print(f"   من حركات المخزون: {total_from_movements}")
            print(f"   الإجمالي المستخدم: {total_dispensed}")

            return total_dispensed

        except Exception as e:
            print(f"خطأ في حساب الكمية المصروفة: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

    def get_entered_quantity(self, item_number):
        """حساب الكمية المدخلة لصنف معين من جدول حركات المخزون"""
        try:
            from database import db_manager

            # حساب مجموع الكميات المدخلة من جدول حركات المخزون الجديد
            query = """
                SELECT COALESCE(SUM(quantity), 0) as total_entered
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """
            result = db_manager.fetch_one(query, (item_number,))

            if result and result['total_entered']:
                return float(result['total_entered'])
            else:
                return 0.0

        except Exception as e:
            print(f"خطأ في حساب الكمية المدخلة للصنف {item_number}: {e}")
            return 0.0
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.status_tree.get_children():
                self.status_tree.delete(item)

            # مسح قاموس معرفات الأصناف
            self.item_ids_map.clear()

            self.status_var.set("جاري تطبيق الفلاتر...")

            # الحصول على قيم الفلاتر
            search_text = self.search_var.get().lower().strip()
            category_filter = self.filter_category_var.get()
            status_filter = self.filter_status_var.get()

            # تحميل البيانات من جدول الأصناف المضافة
            from models import AddedItem
            added_items = AddedItem.get_all()

            total_items = 0
            sequence = 1

            for item in added_items:
                try:
                    # تطبيق فلتر البحث
                    if search_text:
                        item_text = f"{item.item_name} {item.item_number}".lower()
                        if search_text not in item_text:
                            continue

                    # تطبيق فلتر نوع العهدة
                    if category_filter != "الكل":
                        if item.custody_type != category_filter:
                            continue

                    # حساب الكميات
                    entered_qty = self.get_entered_quantity(item.item_number)
                    dispensed_qty = self.get_dispensed_quantity(item.item_number)
                    current_qty = max(0, entered_qty - dispensed_qty)

                    # تحديد حالة المخزون
                    if current_qty <= 0:
                        stock_status = "نفد"
                    elif current_qty <= 5:
                        stock_status = "منخفض"
                    elif current_qty <= 10:
                        stock_status = "تحذير"
                    else:
                        stock_status = "متوفر"

                    # تطبيق فلتر حالة المخزون
                    if status_filter != "الكل":
                        if stock_status != status_filter:
                            continue

                    # إعداد البيانات للعرض
                    values = (
                        sequence,
                        item.item_number or "",
                        item.item_name or "",
                        item.custody_type or "مستهلكة",
                        item.classification or "",
                        int(entered_qty),
                        int(dispensed_qty),
                        int(current_qty),
                        item.unit or "قطعة",
                        "إضافة حركة"
                    )

                    # تحديد لون الصف حسب حالة المخزون
                    tags = []
                    if stock_status == "نفد":
                        tags.append("out_of_stock")
                    elif stock_status == "منخفض":
                        tags.append("low_stock")
                    elif stock_status == "تحذير":
                        tags.append("warning_stock")

                    # إدراج البيانات في الجدول
                    tree_item_id = self.status_tree.insert('', 'end', values=values, tags=tags)

                    # حفظ معرف الصنف في القاموس
                    self.item_ids_map[tree_item_id] = {
                        'item_id': item.id,
                        'item_number': item.item_number,
                        'item_name': item.item_name
                    }

                    sequence += 1
                    total_items += 1

                except Exception as item_error:
                    print(f"⚠️ خطأ في معالجة الصنف {item.item_number}: {item_error}")
                    continue

            # تحديث شريط الحالة
            if total_items == 0:
                self.status_var.set("لا توجد أصناف تطابق معايير البحث")
            else:
                self.status_var.set(f"تم عرض {total_items} صنف من أصل {len(added_items)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق الفلاتر: {e}")
            self.status_var.set("خطأ في تطبيق الفلاتر")

    def filter_status(self):
        """فلترة حالة المخزون - للتوافق مع الكود القديم"""
        self.apply_filters()

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_var.set("")
        self.filter_category_var.set("الكل")
        self.filter_status_var.set("الكل")
        self.load_inventory_status()
    
    def on_item_click(self, event):
        """معالج النقر على عنصر في الجدول"""
        # تحديد العنصر والعمود المنقور عليه
        item_id = self.status_tree.identify_row(event.y)
        column = self.status_tree.identify_column(event.x)

        # تحويل رقم العمود من النص إلى رقم
        try:
            col_num = int(column.replace('#', ''))
            # عمود الإجراءات هو العمود الأخير (رقم 10 في العرض، لكن فهرس 9 في البرمجة)
            # الأعمدة: 1=sequence, 2=item_number, 3=item_name, 4=custody_type, 5=classification,
            # 6=entered_qty, 7=dispensed_qty, 8=current_qty, 9=unit, 10=actions
            if item_id and col_num == 10:  # العمود الأخير (actions)
                # فتح شاشة إضافة حركة مخزون
                self.open_add_movement_window(item_id)
        except (ValueError, AttributeError):
            # في حالة عدم تمكن من تحديد العمود، لا نفعل شيء
            pass

    def on_item_double_click(self, event):
        """معالج النقر المزدوج على صنف"""
        selection = self.status_tree.selection()
        if selection:
            item = self.status_tree.item(selection[0])
            values = item['values']

            # عرض تفاصيل الصنف
            details = f"""
تفاصيل الصنف:

التسلسل: {values[0]}
رقم الصنف: {values[1]}
اسم الصنف: {values[2]}
نوع العهدة: {values[3]}
التصنيف: {values[4]}
الكمية المضافة: {values[5]}
الكمية المصروفة: {values[6]}
الكمية الحالية: {values[7]}
وحدة الصرف: {values[8]}
            """
            messagebox.showinfo("تفاصيل الصنف", details)

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # تحديد العنصر المنقور عليه
        item_id = self.status_tree.identify_row(event.y)
        if item_id:
            # تحديد العنصر
            self.status_tree.selection_set(item_id)
            self.status_tree.focus(item_id)

            # إنشاء القائمة السياقية
            context_menu = tk.Menu(self.status_window, tearoff=0)
            context_menu.add_command(
                label="👁️ معاينة الصنف",
                command=lambda: self.show_item_preview(item_id)
            )
            context_menu.add_command(
                label="➕ إضافة حركة مخزون",
                command=lambda: self.open_add_movement_window(item_id)
            )

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def show_item_preview(self, item_id):
        """عرض شاشة معاينة الصنف"""
        try:
            # الحصول على بيانات الصنف
            item_data = self.get_item_data(item_id)
            if item_data:
                from ui.item_preview_window import ItemPreviewWindow
                preview_window = ItemPreviewWindow(self.status_window, item_data)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة معاينة الصنف: {e}")

    def show_item_edit(self, item_id):
        """عرض شاشة تعديل الصنف"""
        try:
            # الحصول على بيانات الصنف
            item_data = self.get_item_data(item_id)
            if item_data:
                from ui.item_edit_window import ItemEditWindow
                edit_window = ItemEditWindow(self.status_window, item_data, self.refresh_data)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة تعديل الصنف: {e}")

    def get_item_data(self, item_id):
        """الحصول على بيانات الصنف من الجدول"""
        try:
            # الحصول على القيم من الجدول
            values = self.status_tree.item(item_id, 'values')
            if not values:
                return None

            # الحصول على معرف الصنف من القاموس
            item_info = self.item_ids_map.get(item_id, {})

            # إنشاء قاموس البيانات
            item_data = {
                'id': item_info.get('item_id'),
                'sequence': values[0],
                'item_number': values[1],
                'item_name': values[2],
                'custody_type': values[3],
                'classification': values[4],
                'entered_qty': values[5],
                'dispensed_qty': values[6],
                'current_qty': values[7],
                'unit': values[8]
            }

            return item_data

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الصنف: {e}")
            return None

    def refresh_data(self):
        """تحديث البيانات بعد التعديل"""
        self.load_inventory_status()

    def add_movement_for_selected(self):
        """إضافة حركة مخزون للصنف المحدد"""
        selection = self.status_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من الجدول أولاً")
            return

        # فتح شاشة إضافة حركة مخزون للصنف المحدد
        self.open_add_movement_window(selection[0])

    def open_add_movement_window(self, tree_item_id):
        """فتح شاشة إضافة حركة مخزون"""
        try:
            # الحصول على بيانات الصنف من القاموس
            if tree_item_id in self.item_ids_map:
                item_data = self.item_ids_map[tree_item_id]
                item_number = item_data['item_number']
                item_name = item_data['item_name']
            else:
                # الحصول على البيانات من الجدول كبديل
                item_values = self.status_tree.item(tree_item_id)['values']
                item_number = item_values[1]
                item_name = item_values[2]

            # فتح شاشة إضافة حركة مخزون
            from ui.add_inventory_movement_window import AddInventoryMovementWindow
            AddInventoryMovementWindow(self.status_window, self.main_window, item_number, item_name)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة حركة المخزون: {e}")
            print(f"خطأ في فتح شاشة الحركة: {e}")
            import traceback
            traceback.print_exc()
    
    def export_status(self):
        """تصدير حالة المخزون"""
        messagebox.showinfo("قريباً", "تصدير حالة المخزون قيد التطوير")
    
    def print_status(self):
        """طباعة حالة المخزون - PDF أفقي بدون معاينة"""
        try:
            from datetime import datetime
            import tempfile
            import os
            import subprocess
            import webbrowser

            # جلب البيانات
            from models import AddedItem
            added_items = AddedItem.get_all()

            if not added_items:
                messagebox.showwarning("تحذير", "لا توجد أصناف لطباعتها")
                return

            # إنشاء HTML للطباعة
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حالة المخزون</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Noto Sans Arabic', 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #ffffff;
            color: #333;
            font-size: 12px;
            line-height: 1.4;
        }}

        .header {{
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 15px;
        }}

        .header h1 {{
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }}

        .header-info {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            font-size: 11px;
            color: #666;
        }}

        .report-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 11px;
        }}

        .report-table th {{
            background-color: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #2c3e50;
            font-weight: bold;
            font-size: 12px;
        }}

        .report-table td {{
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #bdc3c7;
            vertical-align: middle;
        }}

        .report-table tbody tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        .report-table tbody tr:nth-child(odd) {{
            background-color: #ffffff;
        }}

        .report-table tbody tr:hover {{
            background-color: #e8f4f8;
        }}

        .available {{
            color: #27ae60;
            font-weight: bold;
        }}

        .unavailable {{
            color: #e74c3c;
            font-weight: bold;
        }}

        .low-stock {{
            color: #f39c12;
            font-weight: bold;
        }}

        .footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #bdc3c7;
            padding-top: 15px;
        }}

        .summary {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
        }}

        .summary-item {{
            text-align: center;
        }}

        .summary-item .number {{
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }}

        .summary-item .label {{
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }}

        @media print {{
            @page {{
                size: A4 landscape;
                margin: 15mm;
            }}

            body {{
                font-size: 10px;
            }}

            .header h1 {{
                font-size: 20px;
            }}

            .report-table {{
                font-size: 9px;
            }}

            .report-table th {{
                font-size: 10px;
                padding: 8px 6px;
            }}

            .report-table td {{
                padding: 6px 4px;
            }}

            .no-print {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 تقرير حالة المخزون</h1>
        <div class="header-info">
            <div>التاريخ: {current_date}</div>
            <div>الوقت: {current_time}</div>
            <div>نظام إدارة المخازن والمستودعات</div>
        </div>
    </div>
"""

            # حساب الإحصائيات
            total_items = len(added_items)
            available_items = 0
            unavailable_items = 0
            low_stock_items = 0
            total_quantity = 0

            # إنشاء جدول البيانات
            html_content += """
    <table class="report-table">
        <thead>
            <tr>
                <th style="width: 5%;">م</th>
                <th style="width: 12%;">رقم الصنف</th>
                <th style="width: 25%;">اسم الصنف</th>
                <th style="width: 12%;">نوع العهدة</th>
                <th style="width: 12%;">التصنيف</th>
                <th style="width: 8%;">الكمية المضافة</th>
                <th style="width: 8%;">الكمية المصروفة</th>
                <th style="width: 8%;">الكمية الحالية</th>
                <th style="width: 8%;">وحدة الصرف</th>
                <th style="width: 10%;">حالة التوفر</th>
            </tr>
        </thead>
        <tbody>
"""

            # إضافة البيانات
            for index, item in enumerate(added_items, 1):
                try:
                    # حساب الكميات من جدول حركات المخزون
                    entered_qty = self.get_entered_quantity(item.item_number)
                    dispensed_qty = self.get_dispensed_quantity(item.item_number)
                    current_qty = max(0, entered_qty - dispensed_qty)  # تأكد من عدم كون الكمية سالبة

                    # تحديد حالة التوفر
                    if current_qty <= 0:
                        availability_status = "غير متوفر"
                        availability_class = "unavailable"
                        unavailable_items += 1
                    elif current_qty <= 10:  # كمية قليلة
                        availability_status = "كمية قليلة"
                        availability_class = "low-stock"
                        low_stock_items += 1
                        available_items += 1
                    else:
                        availability_status = "متوفر"
                        availability_class = "available"
                        available_items += 1

                    total_quantity += current_qty

                    html_content += f"""
            <tr>
                <td>{index}</td>
                <td>{item.item_number or ''}</td>
                <td style="text-align: right;">{item.item_name or ''}</td>
                <td>{item.custody_type or ''}</td>
                <td>{item.classification or ''}</td>
                <td>{int(entered_qty)}</td>
                <td>{int(dispensed_qty)}</td>
                <td>{int(current_qty)}</td>
                <td>{item.unit or ''}</td>
                <td class="{availability_class}">{availability_status}</td>
            </tr>
"""
                except Exception as e:
                    print(f"خطأ في معالجة الصنف {item.item_number}: {e}")
                    continue

            html_content += """
        </tbody>
    </table>
"""

            # إضافة الملخص
            html_content += f"""
    <div class="summary">
        <div class="summary-item">
            <div class="number">{total_items}</div>
            <div class="label">إجمالي الأصناف</div>
        </div>
        <div class="summary-item">
            <div class="number available">{available_items}</div>
            <div class="label">أصناف متوفرة</div>
        </div>
        <div class="summary-item">
            <div class="number low-stock">{low_stock_items}</div>
            <div class="label">أصناف قليلة</div>
        </div>
        <div class="summary-item">
            <div class="number unavailable">{unavailable_items}</div>
            <div class="label">أصناف غير متوفرة</div>
        </div>
        <div class="summary-item">
            <div class="number">{int(total_quantity)}</div>
            <div class="label">إجمالي الكمية الحالية</div>
        </div>
    </div>
"""

            html_content += f"""
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن والمستودعات</p>
        <p>تاريخ الطباعة: {current_date} - الوقت: {current_time}</p>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {{
            setTimeout(function() {{
                window.print();
            }}, 500);
        }};
    </script>
</body>
</html>
"""

            # حفظ الملف المؤقت وفتحه للطباعة
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file_path = f.name

            # فتح الملف في المتصفح للطباعة التلقائية
            webbrowser.open('file://' + temp_file_path)

            # رسالة نجاح (بدون رسالة نجاح حسب التفضيلات)
            print("✅ تم إنشاء تقرير حالة المخزون وفتحه للطباعة")

        except Exception as e:
            print(f"❌ خطأ في طباعة حالة المخزون: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"فشل في طباعة حالة المخزون: {e}")

    def on_window_close(self):
        """معالج إغلاق النافذة"""
        try:
            # إلغاء تسجيل النافذة
            from ui.add_inventory_movement_window import unregister_inventory_window
            unregister_inventory_window(self)
        except Exception as e:
            print(f"⚠️ خطأ في إلغاء تسجيل نافذة حالة المخزون: {e}")
        finally:
            # إغلاق النافذة
            self.status_window.destroy()

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = InventoryStatusWindow(root, None)
    root.mainloop()
