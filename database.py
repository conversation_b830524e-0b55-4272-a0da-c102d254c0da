"""
إدارة قاعدة البيانات - تطبيق إدارة المخازن
Database Management - Desktop Stores Management System
"""

import sqlite3
import hashlib
import logging
from datetime import datetime, timedelta, date
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
import json
import bcrypt
import sys
import os

# تصحيح مسار قاعدة البيانات لملف exe
def get_safe_database_path():
    """الحصول على مسار آمن لقاعدة البيانات"""
    if getattr(sys, 'frozen', False):
        # البرنامج يعمل كملف exe - استخدم مجلد المستندات
        documents_path = Path.home() / "Documents"
        app_data_dir = documents_path / "Desktop_Stores_Data"
        app_data_dir.mkdir(exist_ok=True)
        
        # إنشاء مجلدات فرعية
        (app_data_dir / "backups").mkdir(exist_ok=True)
        (app_data_dir / "reports").mkdir(exist_ok=True)
        (app_data_dir / "logs").mkdir(exist_ok=True)
        
        return str(app_data_dir / "stores_management.db")
    else:
        # البرنامج يعمل من الكود المصدري
        current_dir = Path(__file__).parent
        return str(current_dir / "stores_management.db")

# استخدام المسار الآمن
SAFE_DATABASE_PATH = get_safe_database_path()

try:
    from config import DATABASE_PATH, DB_CONFIG, SECURITY_CONFIG, get_message
    # استبدال مسار قاعدة البيانات بالمسار الآمن
    DATABASE_PATH = SAFE_DATABASE_PATH
except ImportError:
    # في حالة عدم وجود config.py، استخدم إعدادات افتراضية
    DATABASE_PATH = SAFE_DATABASE_PATH
    DB_CONFIG = {
        "timeout": 30,
        "check_same_thread": False,
        "isolation_level": None,
        "detect_types": 0  # تعطيل التحويل التلقائي للأنواع لتجنب مشاكل البيانات المعطوبة
    }
    SECURITY_CONFIG = {
        "admin_default_password": "admin"
    }
    def get_message(key, default=""):
        return default

from config import DATABASE_PATH, DB_CONFIG, SECURITY_CONFIG, get_message

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or DATABASE_PATH
        self.connection = None
        self.init_database()
    
    def connect(self) -> sqlite3.Connection:
        """الاتصال بقاعدة البيانات"""
        try:
            if self.connection is None:
                self.connection = sqlite3.connect(
                    self.db_path,
                    timeout=DB_CONFIG["timeout"],
                    check_same_thread=DB_CONFIG["check_same_thread"],
                    isolation_level=DB_CONFIG["isolation_level"],
                    detect_types=DB_CONFIG["detect_types"]
                )
                self.connection.row_factory = sqlite3.Row
                # تعطيل المفاتيح الخارجية مؤقتاً لتجنب مشاكل البيانات
                self.connection.execute("PRAGMA foreign_keys = OFF")
                
                # تحسينات الأداء
                self.connection.execute("PRAGMA journal_mode = WAL")
                self.connection.execute("PRAGMA synchronous = NORMAL")
                self.connection.execute("PRAGMA cache_size = 10000")
                self.connection.execute("PRAGMA temp_store = MEMORY")
                self.connection.execute("PRAGMA mmap_size = 268435456")
            return self.connection
        except sqlite3.Error as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None

    def begin_transaction(self):
        """بدء معاملة جديدة"""
        try:
            conn = self.connect()
            # التحقق من عدم وجود معاملة نشطة
            try:
                conn.execute("BEGIN")
                logger.debug("تم بدء معاملة جديدة")
            except sqlite3.OperationalError as e:
                if "cannot start a transaction within a transaction" in str(e):
                    logger.debug("معاملة نشطة بالفعل")
                else:
                    raise
        except sqlite3.Error as e:
            logger.error(f"خطأ في بدء المعاملة: {e}")
            raise

    def commit(self):
        """تأكيد المعاملة"""
        try:
            if self.connection:
                self.connection.commit()
                logger.debug("تم تأكيد المعاملة")
        except sqlite3.Error as e:
            logger.error(f"خطأ في تأكيد المعاملة: {e}")
            raise

    def rollback(self):
        """إلغاء المعاملة"""
        try:
            if self.connection:
                self.connection.rollback()
                logger.debug("تم إلغاء المعاملة")
        except sqlite3.Error as e:
            logger.error(f"خطأ في إلغاء المعاملة: {e}")
            raise

    def is_connected(self) -> bool:
        """التحقق من حالة الاتصال"""
        try:
            if self.connection is None:
                return False
            # اختبار الاتصال بتنفيذ استعلام بسيط
            self.connection.execute("SELECT 1")
            return True
        except sqlite3.Error:
            return False

    def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال"""
        return {
            "database_path": self.db_path,
            "is_connected": self.is_connected(),
            "connection_object": self.connection is not None
        }
    
    def execute_query(self, query: str, params: tuple = None, auto_commit: bool = True) -> sqlite3.Cursor:
        """تنفيذ استعلام"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # تأكيد تلقائي إذا لم يتم تعطيله
            if auto_commit:
                conn.commit()

            return cursor
        except sqlite3.Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            logger.error(f"الاستعلام: {query}")
            if params:
                logger.error(f"المعاملات: {params}")

            # إلغاء المعاملة في حالة الخطأ
            try:
                if self.connection:
                    self.connection.rollback()
            except sqlite3.Error as rollback_error:
                logger.error(f"خطأ في إلغاء المعاملة: {rollback_error}")

            raise
    
    def fetch_one(self, query: str, params: tuple = None) -> Optional[sqlite3.Row]:
        """جلب سجل واحد"""
        cursor = self.execute_query(query, params, auto_commit=False)
        return cursor.fetchone()
    
    def fetch_all(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """جلب جميع السجلات"""
        cursor = self.execute_query(query, params, auto_commit=False)
        return cursor.fetchall()

    def execute_transaction(self, queries_with_params: List[tuple]) -> bool:
        """تنفيذ عدة استعلامات في معاملة واحدة"""
        try:
            self.begin_transaction()

            for query_data in queries_with_params:
                if len(query_data) == 2:
                    query, params = query_data
                    self.execute_query(query, params, auto_commit=False)
                else:
                    query = query_data[0]
                    self.execute_query(query, auto_commit=False)

            self.commit()
            return True

        except Exception as e:
            logger.error(f"خطأ في تنفيذ المعاملة: {e}")
            self.rollback()
            return False
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # جدول المستخدمين
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    is_admin BOOLEAN DEFAULT 0,
                    last_login DATETIME,
                    failed_login_attempts INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الأدوار
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول ربط المستخدمين بالأدوار
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS user_roles (
                    user_id INTEGER,
                    role_id INTEGER,
                    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (user_id, role_id),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
                )
            """)
            
            # جدول الصلاحيات
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    role_id INTEGER,
                    module_name TEXT NOT NULL,
                    can_view BOOLEAN DEFAULT 0,
                    can_add BOOLEAN DEFAULT 0,
                    can_edit BOOLEAN DEFAULT 0,
                    can_delete BOOLEAN DEFAULT 0,
                    can_print BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
                )
            """)
            
            # جدول الإدارات
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    unit_id INTEGER,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (unit_id) REFERENCES units(id)
                )
            """)
            
            # جدول الأقسام
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS sections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    department_id INTEGER,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments(id)
                )
            """)

            # جدول الوحدات
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS units (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول المستفيدين
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS beneficiaries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    number TEXT UNIQUE,
                    rank TEXT,
                    unit_id INTEGER,
                    department_id INTEGER,
                    section_id INTEGER,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    notes TEXT,
                    data_entry_user_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    entry_date DATE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (unit_id) REFERENCES units(id),
                    FOREIGN KEY (department_id) REFERENCES departments(id),
                    FOREIGN KEY (section_id) REFERENCES sections(id),
                    FOREIGN KEY (data_entry_user_id) REFERENCES users(id)
                )
            """)
            
            # جدول فئات الأصناف
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    parent_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES categories(id)
                )
            """)
            
            # جدول الأصناف
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    code TEXT UNIQUE,
                    description TEXT,
                    category_id INTEGER,
                    unit TEXT NOT NULL,
                    current_quantity REAL DEFAULT 0,
                    minimum_quantity REAL DEFAULT 0,
                    maximum_quantity REAL DEFAULT 0,
                    unit_price REAL DEFAULT 0,
                    custody_type TEXT,
                    classification TEXT,
                    location TEXT,
                    supplier TEXT,
                    barcode TEXT,
                    expiry_date DATE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id)
                )
            """)
            
            # جدول حركة المخزون
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL DEFAULT 0,
                    total_value REAL DEFAULT 0,
                    movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    reference_number TEXT,
                    notes TEXT,
                    user_id INTEGER,
                    transaction_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES items(id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (transaction_id) REFERENCES transactions(id)
                )
            """)
            
            # جدول عمليات الصرف
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_number TEXT UNIQUE NOT NULL,
                    beneficiary_id INTEGER NOT NULL,
                    receiver_id INTEGER,
                    transaction_date DATE NOT NULL,
                    transaction_type TEXT DEFAULT 'صرف',
                    status TEXT DEFAULT 'مكتمل',
                    notes TEXT,
                    total_amount REAL DEFAULT 0,
                    user_id INTEGER,
                    approved_by INTEGER,
                    approved_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (beneficiary_id) REFERENCES beneficiaries(id),
                    FOREIGN KEY (receiver_id) REFERENCES beneficiaries(id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (approved_by) REFERENCES users(id)
                )
            """)
            
            # جدول تفاصيل عمليات الصرف
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS transaction_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id INTEGER NOT NULL,
                    item_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الجدول التنظيمي
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS organizational_chart (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sequence_number INTEGER,
                    item_code TEXT NOT NULL UNIQUE,
                    item_name TEXT NOT NULL UNIQUE,
                    unit TEXT,
                    quantity REAL DEFAULT 0,
                    notes TEXT,
                    department_id INTEGER,
                    section_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments(id),
                    FOREIGN KEY (section_id) REFERENCES sections(id)
                )
            """)

            # جدول الأصناف المضافة
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS added_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_number TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    custody_type TEXT,
                    classification TEXT,
                    unit TEXT NOT NULL,
                    current_quantity INTEGER DEFAULT 0,
                    data_entry_user TEXT,
                    entry_date TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    dispensed_quantity INTEGER DEFAULT 0,
                    barcode TEXT
                )
            """)

            # إضافة حقل الباركود إذا لم يكن موجوداً
            try:
                self.execute_query("ALTER TABLE added_items ADD COLUMN barcode TEXT")
            except Exception:
                # الحقل موجود بالفعل
                pass
            
            # جدول سجل العمليات
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    table_name TEXT,
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            # جدول الإعدادات
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    category TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء الفهارس
            self.create_indexes()

            # تطبيق التحديثات على قاعدة البيانات
            self.apply_migrations()

            # إدراج البيانات الأولية
            self.insert_initial_data()
            
            logger.info("تم إنشاء قاعدة البيانات بنجاح")
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
            raise
    
    def create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_beneficiaries_number ON beneficiaries(number)",
            "CREATE INDEX IF NOT EXISTS idx_items_code ON items(code)",
            "CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_number ON transactions(transaction_number)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_item ON inventory_movements(item_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_date ON inventory_movements(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_log_date ON audit_log(created_at)",
        ]
        
        for index_sql in indexes:
            try:
                self.execute_query(index_sql)
            except sqlite3.Error as e:
                logger.warning(f"تحذير في إنشاء الفهرس: {e}")

    def apply_migrations(self):
        """تطبيق التحديثات على قاعدة البيانات"""
        try:
            # التحقق من وجود عمود movement_date في جدول inventory_movements
            cursor = self.execute_query("PRAGMA table_info(inventory_movements)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'movement_date' not in columns:
                # إضافة عمود movement_date
                self.execute_query("""
                    ALTER TABLE inventory_movements
                    ADD COLUMN movement_date DATETIME DEFAULT CURRENT_TIMESTAMP
                """)
                logger.info("تم إضافة عمود movement_date إلى جدول inventory_movements")

            if 'total_value' not in columns:
                # إضافة عمود total_value
                self.execute_query("""
                    ALTER TABLE inventory_movements
                    ADD COLUMN total_value REAL DEFAULT 0
                """)
                logger.info("تم إضافة عمود total_value إلى جدول inventory_movements")

            if 'reference_number' not in columns:
                # إضافة عمود reference_number
                self.execute_query("""
                    ALTER TABLE inventory_movements
                    ADD COLUMN reference_number TEXT
                """)
                logger.info("تم إضافة عمود reference_number إلى جدول inventory_movements")

            # تحديث البيانات الموجودة
            self.execute_query("""
                UPDATE inventory_movements
                SET movement_date = created_at
                WHERE movement_date IS NULL
            """)

            # التحقق من وجود عمود unit_id في جدول beneficiaries
            cursor = self.execute_query("PRAGMA table_info(beneficiaries)")
            beneficiaries_columns = [row[1] for row in cursor.fetchall()]

            if 'unit_id' not in beneficiaries_columns:
                # إضافة عمود unit_id
                self.execute_query("""
                    ALTER TABLE beneficiaries
                    ADD COLUMN unit_id INTEGER REFERENCES units(id)
                """)
                logger.info("تم إضافة عمود unit_id إلى جدول beneficiaries")

            if 'notes' not in beneficiaries_columns:
                # إضافة عمود notes
                self.execute_query("""
                    ALTER TABLE beneficiaries
                    ADD COLUMN notes TEXT
                """)
                logger.info("تم إضافة عمود notes إلى جدول beneficiaries")

            if 'data_entry_user_id' not in beneficiaries_columns:
                # إضافة عمود data_entry_user_id
                self.execute_query("""
                    ALTER TABLE beneficiaries
                    ADD COLUMN data_entry_user_id INTEGER REFERENCES users(id)
                """)
                logger.info("تم إضافة عمود data_entry_user_id إلى جدول beneficiaries")

            # التحقق من وجود عمود item_code في جدول organizational_chart
            cursor = self.execute_query("PRAGMA table_info(organizational_chart)")
            org_chart_columns = [row[1] for row in cursor.fetchall()]

            if 'item_code' not in org_chart_columns:
                # إضافة عمود item_code
                self.execute_query("""
                    ALTER TABLE organizational_chart
                    ADD COLUMN item_code TEXT
                """)
                logger.info("تم إضافة عمود item_code إلى جدول organizational_chart")

                # تحديث البيانات الموجودة بأرقام تسلسلية
                self.execute_query("""
                    UPDATE organizational_chart
                    SET item_code = 'ITEM' || PRINTF('%04d', id)
                    WHERE item_code IS NULL OR item_code = ''
                """)
                logger.info("تم تحديث أرقام الأصناف الموجودة")

            # التحقق من وجود عمود is_active في جدول units
            cursor = self.execute_query("PRAGMA table_info(units)")
            units_columns = [row[1] for row in cursor.fetchall()]

            if 'is_active' not in units_columns:
                # إضافة عمود is_active
                self.execute_query("""
                    ALTER TABLE units
                    ADD COLUMN is_active BOOLEAN DEFAULT 1
                """)
                logger.info("تم إضافة عمود is_active إلى جدول units")

            # التحقق من وجود عمود is_active في جدول departments
            cursor = self.execute_query("PRAGMA table_info(departments)")
            departments_columns = [row[1] for row in cursor.fetchall()]

            if 'is_active' not in departments_columns:
                # إضافة عمود is_active
                self.execute_query("""
                    ALTER TABLE departments
                    ADD COLUMN is_active BOOLEAN DEFAULT 1
                """)
                logger.info("تم إضافة عمود is_active إلى جدول departments")

            # التحقق من وجود عمود is_active في جدول sections
            cursor = self.execute_query("PRAGMA table_info(sections)")
            sections_columns = [row[1] for row in cursor.fetchall()]

            if 'is_active' not in sections_columns:
                # إضافة عمود is_active
                self.execute_query("""
                    ALTER TABLE sections
                    ADD COLUMN is_active BOOLEAN DEFAULT 1
                """)
                logger.info("تم إضافة عمود is_active إلى جدول sections")

            # التحقق من وجود عمود receiver_id في جدول transactions
            cursor = self.execute_query("PRAGMA table_info(transactions)")
            transactions_columns = [row[1] for row in cursor.fetchall()]

            if 'receiver_id' not in transactions_columns:
                # إضافة عمود receiver_id
                self.execute_query("""
                    ALTER TABLE transactions
                    ADD COLUMN receiver_id INTEGER REFERENCES beneficiaries(id)
                """)
                logger.info("تم إضافة عمود receiver_id إلى جدول transactions")

            # التحقق من وجود عمود status_number في جدول organizational_chart
            cursor = self.execute_query("PRAGMA table_info(organizational_chart)")
            org_chart_columns = [row[1] for row in cursor.fetchall()]

            if 'status_number' not in org_chart_columns:
                # إضافة عمود status_number
                self.execute_query("""
                    ALTER TABLE organizational_chart
                    ADD COLUMN status_number INTEGER DEFAULT 0
                """)
                logger.info("تم إضافة عمود status_number إلى جدول organizational_chart")

        except sqlite3.Error as e:
            logger.warning(f"تحذير في تطبيق التحديثات: {e}")

    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        try:
            # التحقق من وجود المستخدم الإداري
            admin_user = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
            
            if not admin_user:
                # إنشاء المستخدم الإداري
                password_hash = self.hash_password(SECURITY_CONFIG["admin_default_password"])
                self.execute_query("""
                    INSERT INTO users (username, password_hash, full_name, is_admin, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, ("admin", password_hash, "مدير النظام", 1, 1))
                
                logger.info("تم إنشاء المستخدم الإداري")
            
            # إنشاء الأدوار الأساسية
            roles = [
                ("admin", "مدير النظام - صلاحيات كاملة"),
                ("manager", "مدير - صلاحيات إدارية"),
                ("user", "مستخدم - صلاحيات محدودة"),
                ("viewer", "مشاهد - عرض فقط")
            ]
            
            for role_name, role_desc in roles:
                existing_role = self.fetch_one("SELECT id FROM roles WHERE name = ?", (role_name,))
                if not existing_role:
                    self.execute_query("""
                        INSERT INTO roles (name, description) VALUES (?, ?)
                    """, (role_name, role_desc))
            
            # ربط المستخدم الإداري بدور المدير
            admin_user = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
            admin_role = self.fetch_one("SELECT id FROM roles WHERE name = ?", ("admin",))
            
            if admin_user and admin_role:
                existing_assignment = self.fetch_one("""
                    SELECT 1 FROM user_roles WHERE user_id = ? AND role_id = ?
                """, (admin_user["id"], admin_role["id"]))
                
                if not existing_assignment:
                    self.execute_query("""
                        INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)
                    """, (admin_user["id"], admin_role["id"]))
            
            # إدراج بيانات تجريبية للوحدات والإدارات والأقسام (معطل - يبدأ فارغ)
            # تم تعطيل البيانات التجريبية - يمكن للمستخدم إضافة البيانات حسب الحاجة

            # sample_units = [("الوحدة الأولى",), ("الوحدة الثانية",), ("الوحدة الثالثة",)]
            # for unit_name, in sample_units:
            #     try:
            #         self.execute_query("INSERT OR IGNORE INTO units (name) VALUES (?)", (unit_name,))
            #     except Exception as e:
            #         logger.warning(f"تحذير في إدراج الوحدة {unit_name}: {e}")

            # sample_departments = [("الإدارة الأولى", 1), ("الإدارة الثانية", 1), ("الإدارة الثالثة", 2)]
            # for dept_name, unit_id in sample_departments:
            #     try:
            #         self.execute_query("INSERT OR IGNORE INTO departments (name, unit_id) VALUES (?, ?)", (dept_name, unit_id))
            #     except Exception as e:
            #         logger.warning(f"تحذير في إدراج الإدارة {dept_name}: {e}")

            # sample_sections = [("القسم الأول", 1), ("القسم الثاني", 1), ("القسم الثالث", 2)]
            # for section_name, dept_id in sample_sections:
            #     try:
            #         self.execute_query("INSERT OR IGNORE INTO sections (name, department_id) VALUES (?, ?)", (section_name, dept_id))
            #     except Exception as e:
            #         logger.warning(f"تحذير في إدراج القسم {section_name}: {e}")

            # إدراج بيانات تجريبية للمستفيدين (معطل - يبدأ فارغ)
            # sample_beneficiaries = [
            #     ("أحمد محمد علي", "12345", "نقيب", 1, 1, 1),
            #     ("سارة أحمد محمود", "12346", "ملازم", 1, 1, 1),
            #     ("محمد عبدالله حسن", "12347", "رائد", 1, 1, 1),
            #     ("فاطمة علي أحمد", "12348", "عريف", 1, 1, 1),
            #     ("عبدالرحمن محمد", "12349", "جندي", 1, 1, 1)
            # ]

            # for name, number, rank, unit_id, dept_id, section_id in sample_beneficiaries:
            #     try:
            #         self.execute_query("""
            #             INSERT OR IGNORE INTO beneficiaries
            #             (name, number, rank, unit_id, department_id, section_id, data_entry_user_id, entry_date)
            #             VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            #         """, (name, number, rank, unit_id, dept_id, section_id, 1, date.today()))
            #     except Exception as e:
            #         logger.warning(f"تحذير في إدراج المستفيد {name}: {e}")

            logger.info("تم تخطي إدراج البيانات التجريبية - ستبدأ قاعدة البيانات فارغة")

            # إدراج بيانات تجريبية للأصناف المضافة (معطل - يبدأ فارغ)
            # تم تعطيل إدراج البيانات التجريبية للأصناف لتبدأ قاعدة البيانات فارغة
            # يمكن للمستخدم إضافة الأصناف حسب الحاجة

            # sample_items = [
            #     ("ITEM001", "أقلام حبر جاف", "قرطاسية", "عام", "قطعة", 100),
            #     ("ITEM002", "دفاتر مكتبية", "قرطاسية", "عام", "دفتر", 50),
            #     ("ITEM003", "ملفات حفظ", "قرطاسية", "عام", "ملف", 75),
            #     ("ITEM004", "آلة حاسبة", "إلكترونيات", "خاص", "قطعة", 25),
            #     ("ITEM005", "كراسي مكتبية", "أثاث", "عام", "كرسي", 30)
            # ]

            # for item_number, item_name, custody_type, classification, unit, quantity in sample_items:
            #     try:
            #         self.execute_query("""
            #             INSERT OR IGNORE INTO added_items
            #             (item_number, item_name, custody_type, classification, unit, current_quantity, data_entry_user, entry_date)
            #             VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            #         """, (item_number, item_name, custody_type, classification, unit, quantity, "مدير النظام", str(date.today())))
            #     except Exception as e:
            #         logger.warning(f"تحذير في إدراج الصنف {item_name}: {e}")

            logger.info("تم تخطي إدراج البيانات التجريبية للأصناف - ستبدأ قاعدة البيانات فارغة")

            logger.info("تم إدراج البيانات الأولية")

        except sqlite3.Error as e:
            logger.error(f"خطأ في إدراج البيانات الأولية: {e}")
    
    @staticmethod
    def hash_password(password: str) -> str:
        """تشفير كلمة المرور"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def backup_database(self, backup_path: str = None) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_{timestamp}.db"
            
            # نسخ قاعدة البيانات
            source_conn = self.connect()
            backup_conn = sqlite3.connect(backup_path)
            source_conn.backup(backup_conn)
            backup_conn.close()
            
            logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not Path(backup_path).exists():
                logger.error(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
                return False
            
            # إغلاق الاتصال الحالي
            self.disconnect()
            
            # نسخ النسخة الاحتياطية
            backup_conn = sqlite3.connect(backup_path)
            restore_conn = sqlite3.connect(self.db_path)
            backup_conn.backup(restore_conn)
            backup_conn.close()
            restore_conn.close()
            
            # إعادة الاتصال
            self.connect()
            
            logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            return False

# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()

def recreate_db_manager():
    """إعادة إنشاء مدير قاعدة البيانات مع الإعدادات الجديدة"""
    global db_manager
    try:
        # إغلاق الاتصال الحالي إن وجد
        if hasattr(db_manager, 'connection') and db_manager.connection:
            db_manager.connection.close()
            db_manager.connection = None
    except:
        pass

    # إنشاء مدير جديد
    db_manager = DatabaseManager()
    return db_manager

def force_reconnect():
    """إجبار إعادة الاتصال بقاعدة البيانات"""
    global db_manager
    try:
        if hasattr(db_manager, 'connection') and db_manager.connection:
            db_manager.connection.close()
            db_manager.connection = None
        # إجبار إعادة الاتصال في المرة القادمة
        db_manager.connect()
    except Exception as e:
        logger.error(f"خطأ في إعادة الاتصال: {e}")
    return db_manager
