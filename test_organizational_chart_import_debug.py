#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عدم ظهور البيانات بعد استيراد الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart
import pandas as pd
import tempfile

def check_database_status():
    """فحص حالة قاعدة البيانات"""
    print("🔍 فحص حالة قاعدة البيانات...")
    
    try:
        # فحص إجمالي البيانات
        total_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")
        total_count = total_result[0] if total_result else 0
        
        # فحص البيانات النشطة
        active_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")
        active_count = active_result[0] if active_result else 0
        
        # فحص البيانات غير النشطة
        inactive_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 0")
        inactive_count = inactive_result[0] if inactive_result else 0
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   📦 إجمالي البيانات: {total_count}")
        print(f"   ✅ البيانات النشطة: {active_count}")
        print(f"   ❌ البيانات غير النشطة: {inactive_count}")
        
        # فحص آخر البيانات المضافة
        recent_data = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, is_active, created_at 
            FROM organizational_chart 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        if recent_data:
            print(f"\n📋 آخر 10 عناصر مضافة:")
            for item in recent_data:
                status = "نشط" if item['is_active'] else "غير نشط"
                print(f"   ID: {item['id']}, التسلسل: {item['sequence_number']}, الاسم: {item['item_name'][:30]}..., الحالة: {status}")
        
        return {
            'total': total_count,
            'active': active_count,
            'inactive': inactive_count
        }
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return None

def test_import_process():
    """اختبار عملية الاستيراد"""
    print("\n🧪 اختبار عملية الاستيراد...")
    
    try:
        # إنشاء ملف Excel تجريبي
        test_data = {
            'اسم الصنف': ['صنف تجريبي 1', 'صنف تجريبي 2', 'صنف تجريبي 3'],
            'رقم الصنف': ['TEST001', 'TEST002', 'TEST003'],
            'اسم المعدة': ['معدة 1', 'معدة 2', 'معدة 3'],
            'الكمية': [10, 20, 30],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف Excel تجريبي: {temp_file_path}")
        
        # فحص الحالة قبل الاستيراد
        print("\n📊 حالة قاعدة البيانات قبل الاستيراد:")
        before_status = check_database_status()
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'TEST%'")
        print("🧹 تم تنظيف البيانات التجريبية السابقة")
        
        # استيراد البيانات
        print("\n📥 بدء استيراد البيانات...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors:
                print(f"      • {error}")
        
        # فحص الحالة بعد الاستيراد
        print("\n📊 حالة قاعدة البيانات بعد الاستيراد:")
        after_status = check_database_status()
        
        # فحص البيانات المستوردة بالتفصيل
        imported_data = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, item_code, unit, is_active, created_at 
            FROM organizational_chart 
            WHERE item_code LIKE 'TEST%'
            ORDER BY created_at DESC
        """)
        
        if imported_data:
            print(f"\n📋 البيانات المستوردة ({len(imported_data)} عنصر):")
            for item in imported_data:
                status = "نشط" if item['is_active'] else "غير نشط"
                print(f"   ID: {item['id']}, التسلسل: {item['sequence_number']}, الاسم: {item['item_name']}, الرقم: {item['item_code']}, المعدة: {item['unit']}, الحالة: {status}")
        else:
            print("❌ لم يتم العثور على البيانات المستوردة في قاعدة البيانات")
        
        # اختبار عرض البيانات من خلال النموذج
        print("\n🔍 اختبار عرض البيانات من خلال النموذج:")
        all_items = OrganizationalChart.get_all(active_only=True)
        active_test_items = [item for item in all_items if item.item_code and item.item_code.startswith('TEST')]
        
        print(f"   📦 إجمالي العناصر النشطة: {len(all_items)}")
        print(f"   🧪 العناصر التجريبية النشطة: {len(active_test_items)}")
        
        if active_test_items:
            print("   📋 العناصر التجريبية النشطة:")
            for item in active_test_items:
                print(f"      • {item.item_name} (رقم: {item.item_code}, تسلسل: {item.sequence_number})")
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'TEST%'")
        
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        return result.success_count > 0 and len(active_test_items) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def fix_inactive_data():
    """إصلاح البيانات غير النشطة"""
    print("\n🔧 محاولة إصلاح البيانات غير النشطة...")
    
    try:
        # فحص البيانات غير النشطة
        inactive_data = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, item_code, is_active, created_at 
            FROM organizational_chart 
            WHERE is_active = 0
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        if inactive_data:
            print(f"📋 وجدت {len(inactive_data)} عنصر غير نشط (عرض أول 10):")
            for item in inactive_data:
                print(f"   ID: {item['id']}, التسلسل: {item['sequence_number']}, الاسم: {item['item_name'][:30]}...")
            
            # تفعيل جميع البيانات غير النشطة
            result = db_manager.execute_query("UPDATE organizational_chart SET is_active = 1 WHERE is_active = 0")
            affected_rows = result.rowcount if result else 0
            
            print(f"✅ تم تفعيل {affected_rows} عنصر")
            
            # فحص النتيجة
            after_fix = check_database_status()
            return True
        else:
            print("✅ لا توجد بيانات غير نشطة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")
        return False

def test_delete_all_functionality():
    """اختبار وظيفة حذف جميع الأصناف"""
    print("\n🗑️ اختبار وظيفة حذف جميع الأصناف...")

    try:
        # إنشاء بيانات تجريبية أولاً
        print("📝 إنشاء بيانات تجريبية للاختبار...")

        test_items = []
        for i in range(1, 4):
            item = OrganizationalChart(
                sequence_number=9000 + i,
                item_code=f"DELETE_TEST_{i:03d}",
                item_name=f"صنف اختبار حذف {i}",
                unit="قطعة",
                quantity=10.0,
                notes=f"اختبار حذف {i}",
                is_active=True
            )
            if item.save():
                test_items.append(item)
                print(f"   ✅ تم إنشاء: {item.item_name}")
            else:
                print(f"   ❌ فشل في إنشاء: صنف اختبار حذف {i}")

        if not test_items:
            print("❌ فشل في إنشاء البيانات التجريبية")
            return False

        # فحص الحالة قبل الحذف
        before_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"📊 عدد الأصناف قبل الحذف: {before_count}")

        # اختبار حذف جميع الأصناف
        print("\n🗑️ تنفيذ حذف جميع الأصناف...")
        delete_result = OrganizationalChart.delete_all()

        print(f"📊 نتيجة عملية الحذف: {delete_result}")

        # فحص الحالة بعد الحذف
        after_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"📊 عدد الأصناف بعد الحذف: {after_count}")

        # فحص البيانات التجريبية المحددة
        remaining_test_items = db_manager.fetch_all("""
            SELECT id, item_name, item_code, is_active
            FROM organizational_chart
            WHERE item_code LIKE 'DELETE_TEST_%'
        """)

        if remaining_test_items:
            print(f"⚠️ لا تزال هناك {len(remaining_test_items)} عنصر تجريبي:")
            for item in remaining_test_items:
                status = "نشط" if item['is_active'] else "غير نشط"
                print(f"   • {item['item_name']} ({item['item_code']}) - {status}")
        else:
            print("✅ تم حذف جميع البيانات التجريبية")

        # تنظيف البيانات التجريبية المتبقية
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'DELETE_TEST_%'")

        return delete_result

    except Exception as e:
        print(f"❌ خطأ في اختبار حذف الأصناف: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_excel_import_with_delete():
    """اختبار استيراد الإكسل مع حذف البيانات"""
    print("\n📊 اختبار استيراد الإكسل مع حذف البيانات...")

    try:
        # فحص الحالة الأولية
        initial_status = check_database_status()
        print(f"📊 الحالة الأولية: {initial_status['active']} عنصر نشط")

        # إنشاء ملف Excel تجريبي
        test_data = {
            'اسم الصنف': ['صنف استيراد 1', 'صنف استيراد 2', 'صنف استيراد 3', 'صنف استيراد 4', 'صنف استيراد 5'],
            'رقم الصنف': ['IMP001', 'IMP002', 'IMP003', 'IMP004', 'IMP005'],
            'اسم المعدة': ['معدة استيراد 1', 'معدة استيراد 2', 'معدة استيراد 3', 'معدة استيراد 4', 'معدة استيراد 5'],
            'الكمية': [15, 25, 35, 45, 55],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3', 'ملاحظة 4', 'ملاحظة 5']
        }

        df = pd.DataFrame(test_data)

        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')

        print(f"📁 تم إنشاء ملف Excel تجريبي: {temp_file_path}")

        # استيراد البيانات
        print("\n📥 بدء استيراد البيانات...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel

        import_result = import_organizational_chart_from_excel(temp_file_path)

        print(f"📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {import_result.success_count}")
        print(f"   🔄 مكرر: {import_result.duplicate_count}")
        print(f"   ❌ أخطاء: {import_result.error_count}")

        # فحص البيانات المستوردة
        after_import_status = check_database_status()
        print(f"📊 بعد الاستيراد: {after_import_status['active']} عنصر نشط")

        # التحقق من البيانات المستوردة
        imported_items = db_manager.fetch_all("""
            SELECT id, item_name, item_code, is_active
            FROM organizational_chart
            WHERE item_code LIKE 'IMP%'
            ORDER BY item_code
        """)

        print(f"📋 البيانات المستوردة ({len(imported_items)} عنصر):")
        for item in imported_items:
            status = "نشط" if item['is_active'] else "غير نشط"
            print(f"   • {item['item_name']} ({item['item_code']}) - {status}")

        # اختبار حذف جميع الأصناف بعد الاستيراد
        print("\n🗑️ اختبار حذف جميع الأصناف بعد الاستيراد...")
        delete_result = OrganizationalChart.delete_all()

        # فحص الحالة بعد الحذف
        after_delete_status = check_database_status()
        print(f"📊 بعد الحذف: {after_delete_status['active']} عنصر نشط")

        # التحقق من البيانات المستوردة بعد الحذف
        remaining_imported = db_manager.fetch_all("""
            SELECT id, item_name, item_code, is_active
            FROM organizational_chart
            WHERE item_code LIKE 'IMP%'
        """)

        if remaining_imported:
            print(f"⚠️ لا تزال هناك {len(remaining_imported)} عنصر مستورد:")
            for item in remaining_imported:
                status = "نشط" if item['is_active'] else "غير نشط"
                print(f"   • {item['item_name']} ({item['item_code']}) - {status}")
        else:
            print("✅ تم حذف جميع البيانات المستوردة")

        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'IMP%'")

        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass

        return import_result.success_count > 0 and delete_result

    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد والحذف: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """تشغيل التشخيص الشامل"""
    print("🚀 بدء تشخيص شامل لمشاكل الجدول التنظيمي")
    print("=" * 70)

    # فحص الحالة الحالية
    print("📊 فحص الحالة الحالية لقاعدة البيانات:")
    current_status = check_database_status()

    if not current_status:
        print("❌ فشل في فحص قاعدة البيانات")
        return False

    # إذا كانت هناك بيانات غير نشطة، حاول إصلاحها
    if current_status['inactive'] > 0:
        print(f"\n⚠️ وجدت {current_status['inactive']} عنصر غير نشط")
        if fix_inactive_data():
            print("✅ تم إصلاح البيانات غير النشطة")
        else:
            print("❌ فشل في إصلاح البيانات غير النشطة")

    # اختبار وظيفة حذف جميع الأصناف
    print("\n" + "="*50)
    print("🗑️ اختبار وظيفة حذف جميع الأصناف")
    if test_delete_all_functionality():
        print("✅ اختبار حذف الأصناف نجح")
    else:
        print("❌ اختبار حذف الأصناف فشل")

    # اختبار عملية الاستيراد
    print("\n" + "="*50)
    print("📥 اختبار عملية الاستيراد")
    if test_import_process():
        print("✅ اختبار الاستيراد نجح - البيانات تظهر بشكل صحيح")
    else:
        print("❌ اختبار الاستيراد فشل - المشكلة لا تزال موجودة")

    # اختبار الاستيراد مع الحذف
    print("\n" + "="*50)
    print("📊 اختبار الاستيراد مع الحذف")
    if test_excel_import_with_delete():
        print("✅ اختبار الاستيراد والحذف نجح")
    else:
        print("❌ اختبار الاستيراد والحذف فشل")

    print("\n" + "="*70)
    print("📋 ملخص التشخيص:")
    print("   • تم فحص حالة قاعدة البيانات")
    print("   • تم اختبار وظيفة حذف جميع الأصناف")
    print("   • تم اختبار عملية الاستيراد")
    print("   • تم اختبار الاستيراد مع الحذف")
    print("   • تم التحقق من عرض البيانات")

    final_status = check_database_status()
    if final_status:
        print(f"\n📊 الحالة النهائية:")
        print(f"   📦 إجمالي البيانات: {final_status['total']}")
        print(f"   ✅ البيانات النشطة: {final_status['active']}")
        print(f"   ❌ البيانات غير النشطة: {final_status['inactive']}")

    return True

if __name__ == "__main__":
    main()
