#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مشكلة الاستيراد وتكرار الأرقام العامة
Test Import Issue and Duplicate General Numbers
"""

import os
import sys
import tempfile
import pandas as pd

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import_with_duplicates():
    """اختبار الاستيراد مع أرقام مكررة"""
    print("🧪 اختبار الاستيراد مع أرقام مكررة...")
    
    try:
        from database import db_manager
        from utils.excel_import_manager import ExcelImportManager
        from models import Beneficiary
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'IMPORT_TEST%'")
        
        # إنشاء بيانات اختبار مع أرقام مكررة
        test_data = [
            {'الاسم': 'مستفيد اختبار 1', 'الرقم العام': 'IMPORT_TEST001', 'الرتبة': 'جندي'},
            {'الاسم': 'مستفيد اختبار 2', 'الرقم العام': 'IMPORT_TEST002', 'الرتبة': 'عريف'},
            {'الاسم': 'مستفيد اختبار 3', 'الرقم العام': 'IMPORT_TEST001', 'الرتبة': 'رقيب'},  # رقم مكرر
            {'الاسم': 'مستفيد اختبار 4', 'الرقم العام': 'IMPORT_TEST003', 'الرتبة': 'ملازم'},
            {'الاسم': 'مستفيد اختبار 5', 'الرقم العام': 'IMPORT_TEST002', 'الرتبة': 'نقيب'},  # رقم مكرر
        ]
        
        # إنشاء ملف Excel مؤقت
        df = pd.DataFrame(test_data)
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_path = temp_file.name
        temp_file.close()
        
        df.to_excel(temp_path, index=False)
        print(f"✅ تم إنشاء ملف اختبار: {temp_path}")
        print(f"📊 عدد الصفوف: {len(test_data)}")
        print(f"🔄 أرقام مكررة متوقعة: 2")
        
        # عد المستفيدين قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE number LIKE 'IMPORT_TEST%'")[0]
        print(f"📊 المستفيدين قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_path,
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        # عرض النتائج
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        print(f"   📝 إجمالي: {result.success_count + result.duplicate_count + result.error_count}")
        
        # عد المستفيدين بعد الاستيراد
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE number LIKE 'IMPORT_TEST%'")[0]
        print(f"📊 المستفيدين بعد الاستيراد: {after_count}")
        
        # التحقق من البيانات المستوردة
        imported_beneficiaries = db_manager.fetch_all("SELECT name, number, rank FROM beneficiaries WHERE number LIKE 'IMPORT_TEST%' ORDER BY number")
        
        print(f"\n👥 المستفيدين المستوردين:")
        for ben in imported_beneficiaries:
            print(f"   📋 {ben[0]} - {ben[1]} - {ben[2]}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_path)
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'IMPORT_TEST%'")
        
        # التحقق من النتائج
        expected_success = 3  # 3 مستفيدين جدد (بدون المكررين)
        expected_duplicates = 2  # 2 مكرر
        
        if result.success_count == expected_success and result.duplicate_count == expected_duplicates:
            print("✅ الاستيراد يعمل بشكل صحيح - يتجنب التكرار")
            return True
        else:
            print(f"❌ مشكلة في الاستيراد:")
            print(f"   متوقع: {expected_success} نجح، {expected_duplicates} مكرر")
            print(f"   فعلي: {result.success_count} نجح، {result.duplicate_count} مكرر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_beneficiaries_display():
    """اختبار عرض المستفيدين بعد الاستيراد"""
    print("🧪 اختبار عرض المستفيدين...")
    
    try:
        from models import Beneficiary
        from database import db_manager
        
        # إنشاء مستفيد اختبار
        test_beneficiary = Beneficiary(
            name="مستفيد عرض اختبار",
            number="DISPLAY_TEST001",
            rank="جندي",
            is_active=True
        )
        
        if test_beneficiary.save():
            print(f"✅ تم إنشاء مستفيد اختبار برقم: {test_beneficiary.id}")
            
            # استرجاع جميع المستفيدين
            all_beneficiaries = Beneficiary.get_all()
            print(f"📊 إجمالي المستفيدين النشطين: {len(all_beneficiaries)}")
            
            # البحث عن المستفيد المنشأ
            found = False
            for ben in all_beneficiaries:
                if ben.number == "DISPLAY_TEST001":
                    found = True
                    print(f"✅ تم العثور على المستفيد: {ben.name}")
                    break
            
            if found:
                print("✅ عرض المستفيدين يعمل بشكل صحيح")
                
                # تنظيف البيانات التجريبية
                db_manager.execute_query("DELETE FROM beneficiaries WHERE number = 'DISPLAY_TEST001'")
                return True
            else:
                print("❌ لم يتم العثور على المستفيد المنشأ")
                return False
        else:
            print("❌ فشل في إنشاء المستفيد")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العرض: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار مشكلة الاستيراد وتكرار الأرقام العامة")
    print("=" * 60)
    
    tests = [
        ("اختبار عرض المستفيدين", test_beneficiaries_display),
        ("اختبار الاستيراد مع أرقام مكررة", test_import_with_duplicates),
    ]
    
    results = []
    
    for test_name, test_function in tests:
        print(f"\n🔄 {test_name}")
        print("-" * 40)
        
        try:
            result = test_function()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
    
    print("-" * 60)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الاستيراد يعمل بشكل صحيح ويتجنب التكرار")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 قد تحتاج إصلاحات إضافية")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
