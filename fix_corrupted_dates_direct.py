#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح البيانات المعطوبة مباشرة بدون detect_types
"""

import sqlite3
from datetime import datetime

def fix_corrupted_dates():
    """إصلاح البيانات المعطوبة مباشرة"""
    print("🔧 إصلاح البيانات المعطوبة مباشرة...")
    
    try:
        # الاتصال بقاعدة البيانات بدون detect_types
        conn = sqlite3.connect('stores_management.db', detect_types=0)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بدون detect_types")
        
        # فحص جدول المعاملات
        cursor.execute("SELECT id, transaction_date FROM transactions")
        all_transactions = cursor.fetchall()
        
        print(f"📊 تم العثور على {len(all_transactions)} معاملة")
        
        corrupted_count = 0
        fixed_count = 0
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for trans in all_transactions:
            trans_id = trans['id']
            trans_date = trans['transaction_date']
            
            print(f"معاملة {trans_id}: {repr(trans_date)} (نوع: {type(trans_date)})")
            
            is_corrupted = False
            
            # فحص البيانات المعطوبة
            if trans_date:
                if isinstance(trans_date, bytes):
                    print(f"  ❌ بيانات ثنائية: {repr(trans_date)}")
                    is_corrupted = True
                elif isinstance(trans_date, str):
                    # فحص البيانات المعطوبة مثل b'06 17:47:37.387051'
                    if (trans_date.startswith("b'") or 
                        trans_date.startswith('b"') or
                        "b'" in trans_date):
                        print(f"  ❌ نص يحتوي على بيانات ثنائية: {repr(trans_date)}")
                        is_corrupted = True
                    else:
                        # محاولة تحليل التاريخ
                        try:
                            test_date = trans_date
                            if '.' in test_date:
                                test_date = test_date.split('.')[0]
                            
                            if ' ' in test_date:
                                datetime.strptime(test_date, '%Y-%m-%d %H:%M:%S')
                            else:
                                datetime.strptime(test_date, '%Y-%m-%d')
                            
                            print(f"  ✅ تاريخ صالح")
                        except ValueError:
                            print(f"  ❌ تاريخ غير صالح: {repr(trans_date)}")
                            is_corrupted = True
                else:
                    print(f"  ❌ نوع بيانات خاطئ: {type(trans_date)}")
                    is_corrupted = True
            
            if is_corrupted:
                corrupted_count += 1
                
                # إصلاح البيانات المعطوبة
                try:
                    cursor.execute("UPDATE transactions SET transaction_date = ? WHERE id = ?", 
                                 (current_date, trans_id))
                    fixed_count += 1
                    print(f"  ✅ تم إصلاح المعاملة {trans_id}")
                except Exception as e:
                    print(f"  ❌ فشل في إصلاح المعاملة {trans_id}: {e}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print(f"\n📊 النتائج:")
        print(f"  - إجمالي المعاملات: {len(all_transactions)}")
        print(f"  - المعاملات المعطوبة: {corrupted_count}")
        print(f"  - المعاملات المُصلحة: {fixed_count}")
        
        if fixed_count > 0:
            print(f"✅ تم إصلاح {fixed_count} معاملة بنجاح")
        else:
            print("✅ لا توجد معاملات تحتاج إصلاح")
    
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        import traceback
        traceback.print_exc()

def test_after_fix():
    """اختبار بعد الإصلاح"""
    print("\n🧪 اختبار بعد الإصلاح...")
    
    try:
        # الاتصال بقاعدة البيانات بدون detect_types
        conn = sqlite3.connect('stores_management.db', detect_types=0)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار الاستعلام الذي كان يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        cursor.execute(transactions_query)
        transactions = cursor.fetchall()
        
        print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح")
        
        # فحص كل معاملة
        for i, trans in enumerate(transactions):
            print(f"\nمعاملة {i+1}:")
            print(f"  التاريخ: {repr(trans['transaction_date'])}")
            print(f"  الرقم: {repr(trans['transaction_number'])}")
            print(f"  المستفيد: {repr(trans['beneficiary_name'])}")
            print(f"  عدد الأصناف: {repr(trans['items_count'])}")
            
            # محاولة معالجة التاريخ
            try:
                date_str = trans['transaction_date']
                if isinstance(date_str, str) and date_str:
                    if '.' in date_str:
                        date_str = date_str.split('.')[0]
                    
                    if ' ' in date_str:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                    
                    print(f"  ✅ تم تحليل التاريخ: {trans_date}")
                else:
                    print(f"  ⚠️ تاريخ فارغ")
            except Exception as e:
                print(f"  ❌ خطأ في تحليل التاريخ: {e}")
            
            # محاولة معالجة عدد الأصناف
            try:
                items_count = trans['items_count']
                if items_count:
                    count_int = int(float(items_count))
                    print(f"  ✅ عدد الأصناف: {count_int}")
                else:
                    print(f"  ⚠️ عدد أصناف فارغ")
            except Exception as e:
                print(f"  ❌ خطأ في معالجة العدد: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 إصلاح البيانات المعطوبة مباشرة")
    print("=" * 60)
    
    # إصلاح البيانات المعطوبة
    fix_corrupted_dates()
    
    # اختبار بعد الإصلاح
    test_after_fix()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الإصلاح المباشر")
