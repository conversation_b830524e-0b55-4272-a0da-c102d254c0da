#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ملف Excel نموذجي للمستفيدين
Create Sample Beneficiaries Excel File
"""

import pandas as pd
import os
from datetime import datetime

def create_sample_excel():
    """إنشاء ملف Excel نموذجي للمستفيدين"""
    print("📝 إنشاء ملف Excel نموذجي للمستفيدين...")
    
    # بيانات نموذجية متنوعة
    sample_data = [
        {
            'الاسم': 'أحمد محمد علي السالم',
            'الرقم العام': 'BEN001',
            'الرتبة': 'جندي',
            'الإدارة': 'الإدارة العامة',
            'الوحدة': 'الوحدة الأولى'
        },
        {
            'الاسم': 'محمد أحمد سالم الخالد',
            'الرقم العام': 'BEN002',
            'الرتبة': 'عريف',
            'الإدارة': 'إدارة الموارد البشرية',
            'الوحدة': 'الوحدة الثانية'
        },
        {
            'الاسم': 'سالم علي محمد الأحمد',
            'الرقم العام': 'BEN003',
            'الرتبة': 'رقيب',
            'الإدارة': 'الإدارة المالية',
            'الوحدة': 'الوحدة الثالثة'
        },
        {
            'الاسم': 'علي سالم أحمد المحمد',
            'الرقم العام': 'BEN004',
            'الرتبة': 'رقيب أول',
            'الإدارة': 'إدارة التدريب',
            'الوحدة': 'الوحدة الرابعة'
        },
        {
            'الاسم': 'خالد محمد علي الأحمد',
            'الرقم العام': 'BEN005',
            'الرتبة': 'ملازم',
            'الإدارة': 'الإدارة الفنية',
            'الوحدة': 'الوحدة الخامسة'
        },
        {
            'الاسم': 'عبدالله أحمد محمد السالم',
            'الرقم العام': 'BEN006',
            'الرتبة': 'ملازم أول',
            'الإدارة': 'إدارة الأمن',
            'الوحدة': 'الوحدة السادسة'
        },
        {
            'الاسم': 'محمود علي سالم الخالد',
            'الرقم العام': 'BEN007',
            'الرتبة': 'نقيب',
            'الإدارة': 'إدارة العمليات',
            'الوحدة': 'الوحدة السابعة'
        },
        {
            'الاسم': 'يوسف محمد أحمد العلي',
            'الرقم العام': 'BEN008',
            'الرتبة': 'رائد',
            'الإدارة': 'الإدارة اللوجستية',
            'الوحدة': 'الوحدة الثامنة'
        },
        {
            'الاسم': 'عمر سالم محمد الأحمد',
            'الرقم العام': 'BEN009',
            'الرتبة': 'مقدم',
            'الإدارة': 'إدارة التخطيط',
            'الوحدة': 'الوحدة التاسعة'
        },
        {
            'الاسم': 'حسن علي أحمد المحمد',
            'الرقم العام': 'BEN010',
            'الرتبة': 'عقيد',
            'الإدارة': 'الإدارة الاستراتيجية',
            'الوحدة': 'الوحدة العاشرة'
        },
        # إضافة بعض البيانات مع أرقام مكررة لاختبار تجنب التكرار
        {
            'الاسم': 'مستفيد مكرر 1',
            'الرقم العام': 'BEN001',  # رقم مكرر
            'الرتبة': 'عريف',
            'الإدارة': 'إدارة أخرى',
            'الوحدة': 'وحدة أخرى'
        },
        {
            'الاسم': 'مستفيد مكرر 2',
            'الرقم العام': 'BEN005',  # رقم مكرر
            'الرتبة': 'نقيب',
            'الإدارة': 'إدارة مختلفة',
            'الوحدة': 'وحدة مختلفة'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data)
    
    # تحديد اسم الملف مع التاريخ والوقت
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"نموذج_مستفيدين_{timestamp}.xlsx"
    
    # حفظ الملف
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel نموذجي: {filename}")
    print(f"📊 عدد الصفوف: {len(sample_data)}")
    print(f"📋 الأعمدة: {', '.join(df.columns)}")
    print(f"🔄 يحتوي على {len([row for row in sample_data if row['الرقم العام'] in ['BEN001', 'BEN005']])} صف مكرر لاختبار تجنب التكرار")
    
    # عرض معاينة البيانات
    print(f"\n📋 معاينة البيانات:")
    print("-" * 80)
    for i, row in enumerate(sample_data[:5], 1):
        print(f"{i:2d}. {row['الاسم']:<25} | {row['الرقم العام']:<8} | {row['الرتبة']:<12} | {row['الإدارة']}")
    
    if len(sample_data) > 5:
        print(f"    ... و {len(sample_data) - 5} صف آخر")
    
    print("-" * 80)
    print(f"📁 مسار الملف: {os.path.abspath(filename)}")
    
    return filename

def create_template_excel():
    """إنشاء قالب Excel فارغ للمستفيدين"""
    print("📝 إنشاء قالب Excel فارغ للمستفيدين...")
    
    # إنشاء DataFrame فارغ مع الأعمدة المطلوبة
    columns = ['الاسم', 'الرقم العام', 'الرتبة', 'الإدارة', 'الوحدة']
    df = pd.DataFrame(columns=columns)
    
    # إضافة صف واحد كمثال
    example_row = {
        'الاسم': 'مثال: أحمد محمد علي',
        'الرقم العام': 'مثال: EMP001',
        'الرتبة': 'مثال: جندي',
        'الإدارة': 'مثال: الإدارة العامة',
        'الوحدة': 'مثال: الوحدة الأولى'
    }
    
    df = pd.concat([df, pd.DataFrame([example_row])], ignore_index=True)
    
    # تحديد اسم الملف
    filename = "قالب_مستفيدين_فارغ.xlsx"
    
    # حفظ الملف
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء قالب Excel فارغ: {filename}")
    print(f"📋 الأعمدة المطلوبة: {', '.join(columns)}")
    print(f"📁 مسار الملف: {os.path.abspath(filename)}")
    print(f"💡 احذف الصف المثال وأضف بياناتك الحقيقية")
    
    return filename

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملفات Excel نموذجية للمستفيدين")
    print("=" * 60)
    
    try:
        # إنشاء ملف نموذجي مع بيانات
        sample_file = create_sample_excel()
        
        print("\n" + "-" * 60)
        
        # إنشاء قالب فارغ
        template_file = create_template_excel()
        
        print("\n" + "=" * 60)
        print("📊 ملخص الملفات المنشأة:")
        print(f"1. ملف نموذجي مع بيانات: {sample_file}")
        print(f"2. قالب فارغ للاستخدام: {template_file}")
        
        print("\n💡 تعليمات الاستخدام:")
        print("• استخدم الملف النموذجي لاختبار الاستيراد")
        print("• استخدم القالب الفارغ لإدخال بياناتك الحقيقية")
        print("• تأكد من وجود جميع الأعمدة المطلوبة")
        print("• الرقم العام يجب أن يكون فريد لكل مستفيد")
        
        print("\n✅ تم إنشاء الملفات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملفات: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
