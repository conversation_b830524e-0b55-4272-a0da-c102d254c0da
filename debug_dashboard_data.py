#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لفحص مشكلة بيانات لوحة التحكم
"""

from database import db_manager

def debug_inventory_movements():
    """فحص جدول حركات المخزون"""
    print("=== فحص جدول inventory_movements_new ===")
    
    try:
        movements = db_manager.fetch_all("SELECT * FROM inventory_movements_new LIMIT 10")
        print(f"عدد الحركات: {len(movements)}")
        
        for i, mov in enumerate(movements):
            print(f"\nحركة {i+1}:")
            for key, value in dict(mov).items():
                print(f"  {key}: {value} (نوع: {type(value)})")
                
    except Exception as e:
        print(f"خطأ في فحص حركات المخزون: {e}")

def debug_transactions():
    """فحص جدول المعاملات"""
    print("\n=== فحص جدول transactions ===")
    
    try:
        transactions = db_manager.fetch_all("SELECT * FROM transactions LIMIT 5")
        print(f"عدد المعاملات: {len(transactions)}")
        
        for i, trans in enumerate(transactions):
            print(f"\nمعاملة {i+1}:")
            for key, value in dict(trans).items():
                print(f"  {key}: {value} (نوع: {type(value)})")
                
    except Exception as e:
        print(f"خطأ في فحص المعاملات: {e}")

def debug_beneficiaries():
    """فحص جدول المستفيدين"""
    print("\n=== فحص جدول beneficiaries ===")
    
    try:
        beneficiaries = db_manager.fetch_all("SELECT * FROM beneficiaries LIMIT 3")
        print(f"عدد المستفيدين: {len(beneficiaries)}")
        
        for i, ben in enumerate(beneficiaries):
            print(f"\nمستفيد {i+1}:")
            for key, value in dict(ben).items():
                print(f"  {key}: {value} (نوع: {type(value)})")
                
    except Exception as e:
        print(f"خطأ في فحص المستفيدين: {e}")

def debug_organizational_chart():
    """فحص الجدول التنظيمي"""
    print("\n=== فحص جدول organizational_chart ===")
    
    try:
        items = db_manager.fetch_all("SELECT COUNT(*) as count FROM organizational_chart WHERE is_active = 1")
        print(f"عدد الأصناف النشطة في الجدول التنظيمي: {items[0]['count']}")
        
        items = db_manager.fetch_all("SELECT COUNT(*) as count FROM organizational_chart")
        print(f"إجمالي الأصناف في الجدول التنظيمي: {items[0]['count']}")
        
    except Exception as e:
        print(f"خطأ في فحص الجدول التنظيمي: {e}")

def debug_added_items():
    """فحص جدول الأصناف المضافة"""
    print("\n=== فحص جدول added_items ===")
    
    try:
        items = db_manager.fetch_all("SELECT COUNT(*) as count FROM added_items WHERE is_active = 1")
        print(f"عدد الأصناف النشطة في المخزون: {items[0]['count']}")
        
        # فحص الأصناف منخفضة المخزون
        low_stock = db_manager.fetch_all("SELECT COUNT(*) as count FROM added_items WHERE current_quantity <= 5 AND is_active = 1")
        print(f"عدد الأصناف منخفضة المخزون (5 فأقل): {low_stock[0]['count']}")
        
    except Exception as e:
        print(f"خطأ في فحص الأصناف المضافة: {e}")

def test_dashboard_calculations():
    """اختبار حسابات لوحة التحكم"""
    print("\n=== اختبار حسابات لوحة التحكم ===")
    
    try:
        from models import OrganizationalChart, Transaction, AddedItem, Beneficiary
        
        # إجمالي أصناف الجدول التنظيمي
        org_items = OrganizationalChart.get_all()
        print(f"إجمالي أصناف الجدول التنظيمي: {len(org_items)}")
        
        # إجمالي المستفيدين
        beneficiaries = Beneficiary.get_all()
        print(f"إجمالي المستفيدين: {len(beneficiaries)}")
        
        # إجمالي المعاملات
        transactions = Transaction.get_all()
        print(f"إجمالي المعاملات: {len(transactions)}")
        
        # الأصناف منخفضة المخزون
        added_items = AddedItem.get_all()
        low_stock_count = 0
        for item in added_items:
            current_qty = item.current_quantity if hasattr(item, 'current_quantity') else 0
            if current_qty <= 5:
                low_stock_count += 1
        
        print(f"الأصناف منخفضة المخزون: {low_stock_count}")
        
    except Exception as e:
        print(f"خطأ في اختبار حسابات لوحة التحكم: {e}")

if __name__ == "__main__":
    print("🔍 بدء فحص بيانات لوحة التحكم...")
    
    debug_organizational_chart()
    debug_added_items()
    debug_transactions()
    debug_beneficiaries()
    debug_inventory_movements()
    test_dashboard_calculations()
    
    print("\n✅ انتهى الفحص")
