#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل مفصل للبيانات المعطوبة في قاعدة البيانات
"""

import sqlite3
from datetime import datetime

def analyze_corrupted_data():
    """تحليل مفصل للبيانات المعطوبة"""
    print("🔍 تحليل مفصل للبيانات المعطوبة...")
    
    try:
        # الاتصال المباشر بقاعدة البيانات
        conn = sqlite3.connect('stores.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("\n=== فحص جدول transactions ===")
        
        # فحص جميع السجلات في جدول المعاملات
        cursor.execute("SELECT COUNT(*) as total FROM transactions")
        total_transactions = cursor.fetchone()['total']
        print(f"إجمالي المعاملات: {total_transactions}")
        
        # فحص البيانات الخام
        cursor.execute("SELECT id, transaction_date FROM transactions LIMIT 10")
        transactions = cursor.fetchall()
        
        print("\nعينة من التواريخ:")
        for i, trans in enumerate(transactions):
            trans_id = trans['id']
            trans_date = trans['transaction_date']
            print(f"معاملة {trans_id}: {repr(trans_date)} (نوع: {type(trans_date)})")
        
        # البحث عن السجلات المعطوبة
        print("\n=== البحث عن السجلات المعطوبة ===")
        
        # البحث بطرق مختلفة
        search_patterns = [
            "06 17:47:37",
            "b'06",
            "387051"
        ]
        
        for pattern in search_patterns:
            try:
                cursor.execute("SELECT id, transaction_date FROM transactions WHERE transaction_date LIKE ?", (f"%{pattern}%",))
                corrupted = cursor.fetchall()
                print(f"السجلات التي تحتوي على '{pattern}': {len(corrupted)}")
                
                for trans in corrupted[:3]:  # عرض أول 3 سجلات فقط
                    print(f"  ID: {trans['id']}, التاريخ: {repr(trans['transaction_date'])}")
                    
            except Exception as e:
                print(f"خطأ في البحث عن '{pattern}': {e}")
        
        # محاولة قراءة جميع التواريخ
        print("\n=== محاولة قراءة جميع التواريخ ===")
        cursor.execute("SELECT id, transaction_date FROM transactions")
        all_transactions = cursor.fetchall()
        
        corrupted_ids = []
        valid_count = 0
        
        for trans in all_transactions:
            try:
                trans_date = trans['transaction_date']
                
                # محاولة تحويل التاريخ
                if isinstance(trans_date, str):
                    datetime.strptime(trans_date, '%Y-%m-%d %H:%M:%S')
                    valid_count += 1
                else:
                    # إذا لم يكن نص، فهو معطوب
                    corrupted_ids.append(trans['id'])
                    print(f"تاريخ معطوب في المعاملة {trans['id']}: {repr(trans_date)}")
                    
            except Exception as e:
                corrupted_ids.append(trans['id'])
                print(f"خطأ في المعاملة {trans['id']}: {e}")
        
        print(f"\nالنتائج:")
        print(f"معاملات صحيحة: {valid_count}")
        print(f"معاملات معطوبة: {len(corrupted_ids)}")
        print(f"المعاملات المعطوبة: {corrupted_ids}")
        
        # محاولة إصلاح البيانات المعطوبة
        if corrupted_ids:
            print(f"\n=== إصلاح {len(corrupted_ids)} معاملة معطوبة ===")
            
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            for trans_id in corrupted_ids:
                try:
                    cursor.execute("UPDATE transactions SET transaction_date = ? WHERE id = ?", 
                                 (current_date, trans_id))
                    print(f"✅ تم إصلاح المعاملة {trans_id}")
                except Exception as e:
                    print(f"❌ فشل في إصلاح المعاملة {trans_id}: {e}")
            
            # حفظ التغييرات
            conn.commit()
            print("✅ تم حفظ التغييرات")
        
        # التحقق من النتائج
        print("\n=== التحقق من النتائج ===")
        cursor.execute("SELECT COUNT(*) as total FROM transactions")
        total_after = cursor.fetchone()['total']
        print(f"إجمالي المعاملات بعد الإصلاح: {total_after}")
        
        # اختبار قراءة جميع التواريخ مرة أخرى
        cursor.execute("SELECT id, transaction_date FROM transactions")
        all_transactions_after = cursor.fetchall()
        
        valid_after = 0
        corrupted_after = 0
        
        for trans in all_transactions_after:
            try:
                trans_date = trans['transaction_date']
                if isinstance(trans_date, str):
                    datetime.strptime(trans_date, '%Y-%m-%d %H:%M:%S')
                    valid_after += 1
                else:
                    corrupted_after += 1
            except:
                corrupted_after += 1
        
        print(f"معاملات صحيحة بعد الإصلاح: {valid_after}")
        print(f"معاملات معطوبة بعد الإصلاح: {corrupted_after}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def test_dashboard_data():
    """اختبار تحميل بيانات لوحة التحكم"""
    print("\n🧪 اختبار تحميل بيانات لوحة التحكم...")
    
    try:
        from database import db_manager
        
        # اختبار المعاملات
        print("اختبار المعاملات...")
        try:
            transactions = db_manager.fetch_all("SELECT * FROM transactions LIMIT 5")
            print(f"✅ تم تحميل {len(transactions)} معاملة")
        except Exception as e:
            print(f"❌ فشل في تحميل المعاملات: {e}")
        
        # اختبار المستفيدين
        print("اختبار المستفيدين...")
        try:
            beneficiaries = db_manager.fetch_all("SELECT * FROM beneficiaries LIMIT 5")
            print(f"✅ تم تحميل {len(beneficiaries)} مستفيد")
        except Exception as e:
            print(f"❌ فشل في تحميل المستفيدين: {e}")
        
        # اختبار الأصناف
        print("اختبار الأصناف...")
        try:
            items = db_manager.fetch_all("SELECT * FROM added_items WHERE is_active = 1 LIMIT 5")
            print(f"✅ تم تحميل {len(items)} صنف")
        except Exception as e:
            print(f"❌ فشل في تحميل الأصناف: {e}")
        
        # اختبار الجدول التنظيمي
        print("اختبار الجدول التنظيمي...")
        try:
            org_items = db_manager.fetch_all("SELECT * FROM organizational_chart WHERE is_active = 1 LIMIT 5")
            print(f"✅ تم تحميل {len(org_items)} عنصر من الجدول التنظيمي")
        except Exception as e:
            print(f"❌ فشل في تحميل الجدول التنظيمي: {e}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار البيانات: {e}")

if __name__ == "__main__":
    print("🚀 تحليل مفصل للبيانات المعطوبة")
    print("=" * 60)
    
    # تحليل البيانات المعطوبة
    analyze_corrupted_data()
    
    # اختبار تحميل البيانات
    test_dashboard_data()
    
    print("\n" + "=" * 60)
    print("✅ انتهى التحليل")
