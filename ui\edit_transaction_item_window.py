"""
نافذة تعديل صنف في عملية الصرف - تطبيق إدارة المخازن
Edit Transaction Item Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

from config import APP_CONFIG, UI_CONFIG, get_message
from models import AddedItem, TransactionItem
from database import db_manager


class EditTransactionItemWindow:
    """نافذة تعديل صنف في عملية الصرف"""
    
    def __init__(self, parent, transaction_item_id, main_window=None, refresh_callback=None):
        self.parent = parent
        self.transaction_item_id = transaction_item_id
        self.main_window = main_window
        self.refresh_callback = refresh_callback
        self.edit_window = None
        self.transaction_item_data = None
        
        # متغيرات النموذج
        self.item_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # قوائم البيانات
        self.items = []
        
        # تحميل البيانات
        self.load_data()
        
        # إعداد النافذة
        self.setup_window()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل بيانات الصنف في العملية
            query = """
                SELECT ti.*, ai.item_name, ai.item_number, ai.unit
                FROM transaction_items ti
                LEFT JOIN added_items ai ON ti.item_id = ai.id
                WHERE ti.id = ?
            """
            result = db_manager.fetch_one(query, (self.transaction_item_id,))
            
            if result:
                self.transaction_item_data = dict(result)
                # تعيين القيم في المتغيرات
                self.item_var.set(f"{self.transaction_item_data.get('item_name', '')} ({self.transaction_item_data.get('item_number', '')})")

                # تحويل الكمية إلى عدد صحيح
                quantity = self.transaction_item_data.get('quantity', 0)
                try:
                    quantity = int(float(quantity)) if quantity else 0
                except (ValueError, TypeError):
                    quantity = 0
                self.quantity_var.set(str(quantity))

                self.notes_var.set(self.transaction_item_data.get('notes', ''))
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على الصنف")
                return
            
            # تحميل الأصناف المتاحة
            self.items = AddedItem.get_all()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
    
    def setup_window(self):
        """إعداد النافذة"""
        self.edit_window = tk.Toplevel(self.parent)
        self.edit_window.title("تعديل صنف في العملية")
        self.edit_window.geometry("650x500")
        self.edit_window.resizable(False, False)

        # توسيط النافذة
        self.edit_window.transient(self.parent)
        self.edit_window.grab_set()

        # حساب موقع التوسيط
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 325
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 250
        self.edit_window.geometry(f"650x500+{x}+{y}")

        # إعداد المحتوى
        self.setup_content()
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.edit_window)
        main_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="📝 تعديل صنف في العملية",
            bootstyle="warning",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 15))

        # نموذج البيانات
        self.create_form(main_frame)

        # أزرار العمليات
        self.create_buttons(main_frame)
    
    def create_form(self, parent):
        """إنشاء نموذج البيانات"""
        form_frame = ttk_bs.LabelFrame(parent, text="بيانات الصنف", bootstyle="warning")
        form_frame.pack(fill=X, pady=(0, 15))

        # إطار داخلي
        inner_frame = ttk_bs.Frame(form_frame)
        inner_frame.pack(fill=X, padx=15, pady=15)
        
        # الصنف (للقراءة فقط)
        item_frame = ttk_bs.Frame(inner_frame)
        item_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(item_frame, text="الصنف", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        item_entry = ttk_bs.Entry(
            item_frame,
            textvariable=self.item_var,
            font=("Arial", 10),
            state="readonly"
        )
        item_entry.pack(fill=X)
        
        # الكمية
        quantity_frame = ttk_bs.Frame(inner_frame)
        quantity_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(quantity_frame, text="الكمية", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        quantity_entry = ttk_bs.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            font=("Arial", 10)
        )
        quantity_entry.pack(fill=X)
        
        # ربط التحقق من الأرقام فقط
        quantity_entry.bind('<KeyPress>', self.validate_number_input)
        
        # الملاحظات
        notes_frame = ttk_bs.Frame(inner_frame)
        notes_frame.pack(fill=X, pady=10)

        ttk_bs.Label(notes_frame, text="ملاحظات", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))

        self.notes_text = tk.Text(
            notes_frame,
            height=2,
            font=("Arial", 10),
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=X)
        
        # إدراج النص الموجود
        if self.notes_var.get():
            self.notes_text.insert(tk.END, self.notes_var.get())
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        # إطار للأزرار بدون LabelFrame لتوفير مساحة
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=15)

        # خط فاصل
        separator = ttk_bs.Separator(buttons_frame, orient='horizontal')
        separator.pack(fill=X, pady=(0, 10))

        # إطار داخلي للأزرار
        inner_buttons = ttk_bs.Frame(buttons_frame)
        inner_buttons.pack()

        # زر الحفظ
        save_btn = ttk_bs.Button(
            inner_buttons,
            text="💾 حفظ التغييرات",
            command=self.save_changes,
            bootstyle="success",
            width=20
        )
        save_btn.pack(side=LEFT, padx=5)

        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            inner_buttons,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="danger",
            width=15
        )
        cancel_btn.pack(side=LEFT, padx=5)
    
    def validate_number_input(self, event):
        """التحقق من إدخال الأرقام الصحيحة فقط"""
        char = event.char
        if char.isdigit() or char in ['\b', '\x7f']:  # أرقام صحيحة، backspace، delete فقط
            return True
        return "break"
    
    def save_changes(self):
        """حفظ التغييرات"""
        try:
            # التحقق من صحة البيانات
            if not self.quantity_var.get():
                messagebox.showwarning("تحذير", "يرجى إدخال الكمية")
                return
            
            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    messagebox.showwarning("تحذير", "يجب أن تكون الكمية أكبر من صفر")
                    return
            except ValueError:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                return
            
            # الحصول على الملاحظات
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            # تحديث بيانات الصنف في العملية
            update_query = """
                UPDATE transaction_items
                SET quantity = ?, notes = ?
                WHERE id = ?
            """
            db_manager.execute_query(update_query, (quantity, notes, self.transaction_item_id))
            
            # رسالة نجاح
            messagebox.showinfo("نجح", "تم حفظ التغييرات بنجاح")
            
            # تحديث الجدول في النافذة الأصلية
            if self.refresh_callback:
                self.refresh_callback()
            
            # تحديث الشاشة الرئيسية
            if hasattr(self.main_window, 'refresh_all_data'):
                self.main_window.refresh_all_data()
            
            # إغلاق النافذة
            self.close_window()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التغييرات: {e}")
    
    def close_window(self):
        """إغلاق النافذة"""
        self.edit_window.destroy()
