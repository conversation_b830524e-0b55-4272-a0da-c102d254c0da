#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مخصص لمشاكل الاستيراد وحذف الأصناف
Specific Test for Import and Delete Issues

هذا الاختبار يركز على:
1. تشخيص مشاكل الاستيراد من Excel
2. تشخيص مشاكل حذف جميع الأصناف
3. فحص حالة قاعدة البيانات
4. اختبار التفعيل والإلغاء
"""

import sys
import os
from pathlib import Path
import pandas as pd
import time
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from database import db_manager
    from models import OrganizationalChart
    print("✅ تم استيراد الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class ImportDeleteDiagnostic:
    """فئة تشخيص مشاكل الاستيراد والحذف"""
    
    def __init__(self):
        print("🔧 بدء تشخيص مشاكل الاستيراد والحذف")
        print("=" * 50)
    
    def check_database_structure(self):
        """فحص بنية قاعدة البيانات"""
        print("🔍 فحص بنية قاعدة البيانات...")
        
        try:
            # فحص وجود الجدول
            tables = db_manager.fetch_all(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='organizational_chart'"
            )
            
            if not tables:
                print("❌ جدول organizational_chart غير موجود")
                print("🔧 محاولة إنشاء الجدول...")
                OrganizationalChart.create_table()
                print("✅ تم إنشاء الجدول")
            else:
                print("✅ جدول organizational_chart موجود")
            
            # فحص بنية الجدول
            columns = db_manager.fetch_all("PRAGMA table_info(organizational_chart)")
            print(f"📊 أعمدة الجدول ({len(columns)}):")
            for col in columns:
                print(f"    - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            # فحص البيانات الحالية
            total_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            active_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            inactive_count = total_count - active_count
            
            print(f"📊 حالة البيانات:")
            print(f"    - إجمالي: {total_count}")
            print(f"    - نشط: {active_count}")
            print(f"    - غير نشط: {inactive_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return False
    
    def create_test_data(self):
        """إنشاء بيانات اختبار"""
        print("\n📝 إنشاء بيانات اختبار...")
        
        try:
            test_items = [
                {
                    'sequence_number': 1,
                    'item_code': 'DIAG001',
                    'item_name': 'صنف تشخيص 1',
                    'unit': 'قطعة',
                    'quantity': 10,
                    'notes': 'اختبار تشخيص',
                    'is_active': True
                },
                {
                    'sequence_number': 2,
                    'item_code': 'DIAG002',
                    'item_name': 'صنف تشخيص 2',
                    'unit': 'كيلو',
                    'quantity': 5,
                    'notes': 'اختبار تشخيص 2',
                    'is_active': True
                },
                {
                    'sequence_number': 3,
                    'item_code': 'DIAG003',
                    'item_name': 'صنف تشخيص غير نشط',
                    'unit': 'متر',
                    'quantity': 3,
                    'notes': 'اختبار غير نشط',
                    'is_active': False
                }
            ]
            
            created_count = 0
            for item_data in test_items:
                item = OrganizationalChart(**item_data)
                if item.save():
                    created_count += 1
                    print(f"✅ تم إنشاء: {item.item_name}")
                else:
                    print(f"❌ فشل في إنشاء: {item_data['item_name']}")
            
            print(f"📊 تم إنشاء {created_count} من أصل {len(test_items)} عنصر")
            return created_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء بيانات الاختبار: {e}")
            return False
    
    def test_get_all_function(self):
        """اختبار دالة get_all"""
        print("\n🔍 اختبار دالة get_all...")
        
        try:
            # اختبار الحصول على العناصر النشطة فقط
            active_items = OrganizationalChart.get_all(active_only=True)
            print(f"📊 العناصر النشطة: {len(active_items)}")
            
            # اختبار الحصول على جميع العناصر
            all_items = OrganizationalChart.get_all(active_only=False)
            print(f"📊 جميع العناصر: {len(all_items)}")
            
            # طباعة تفاصيل العناصر
            print("📋 تفاصيل العناصر النشطة:")
            for item in active_items[:5]:  # أول 5 عناصر فقط
                print(f"    - {item.sequence_number}: {item.item_name} ({item.item_code}) - نشط: {item.is_active}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار get_all: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_delete_all_function(self):
        """اختبار دالة delete_all"""
        print("\n🗑️ اختبار دالة delete_all...")
        
        try:
            # حالة قبل الحذف
            before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            before_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            print(f"📊 قبل الحذف - نشط: {before_active}, إجمالي: {before_total}")
            
            # تشغيل دالة الحذف
            print("🔄 تشغيل دالة delete_all...")
            result = OrganizationalChart.delete_all()
            print(f"📊 نتيجة delete_all: {result}")
            
            # حالة بعد الحذف
            after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            after_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            print(f"📊 بعد الحذف - نشط: {after_active}, إجمالي: {after_total}")
            
            # تحليل النتائج
            deleted_count = before_active - after_active
            print(f"📊 تم حذف (تعطيل) {deleted_count} عنصر")
            
            # فحص العناصر المتبقية
            remaining_items = OrganizationalChart.get_all(active_only=True)
            print(f"📊 العناصر النشطة المتبقية: {len(remaining_items)}")
            
            if remaining_items:
                print("📋 العناصر المتبقية:")
                for item in remaining_items:
                    print(f"    - {item.item_name} ({item.item_code})")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار delete_all: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_excel_test_file(self):
        """إنشاء ملف Excel للاختبار"""
        print("\n📄 إنشاء ملف Excel للاختبار...")
        
        try:
            test_data = [
                {
                    'التسلسل': 1,
                    'رقم الصنف': 'EXCEL001',
                    'اسم الصنف': 'صنف من Excel 1',
                    'اسم المعدة': 'معدة Excel 1',
                    'الكمية': 15,
                    'الملاحظات': 'مستورد من Excel'
                },
                {
                    'التسلسل': 2,
                    'رقم الصنف': 'EXCEL002',
                    'اسم الصنف': 'صنف من Excel 2',
                    'اسم المعدة': 'معدة Excel 2',
                    'الكمية': 25,
                    'الملاحظات': 'مستورد من Excel'
                },
                {
                    'التسلسل': 3,
                    'رقم الصنف': '',  # بدون رقم صنف
                    'اسم الصنف': 'صنف بدون رقم',
                    'اسم المعدة': 'معدة بدون رقم',
                    'الكمية': 5,
                    'الملاحظات': 'اختبار بدون رقم صنف'
                }
            ]
            
            df = pd.DataFrame(test_data)
            excel_file = project_root / "test_import_diagnostic.xlsx"
            df.to_excel(excel_file, index=False, engine='openpyxl')
            
            print(f"✅ تم إنشاء ملف Excel: {excel_file}")
            print(f"📊 عدد الصفوف: {len(test_data)}")
            
            return excel_file
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف Excel: {e}")
            return None
    
    def test_excel_import(self):
        """اختبار استيراد Excel"""
        print("\n📥 اختبار استيراد Excel...")
        
        try:
            # إنشاء ملف Excel
            excel_file = self.create_excel_test_file()
            if not excel_file:
                return False
            
            # حالة قبل الاستيراد
            before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            print(f"📊 عدد العناصر قبل الاستيراد: {before_count}")
            
            # استيراد الملف
            from utils.organizational_chart_import import import_organizational_chart_from_excel
            
            print("🔄 بدء عملية الاستيراد...")
            result = import_organizational_chart_from_excel(str(excel_file))
            
            print(f"📊 نتائج الاستيراد:")
            print(f"    - نجح: {result.success_count}")
            print(f"    - مكرر: {result.duplicate_count}")
            print(f"    - خطأ: {result.error_count}")
            print(f"    - وقت المعالجة: {result.processing_time:.2f} ثانية")
            
            if hasattr(result, 'errors') and result.errors:
                print("⚠️ أخطاء الاستيراد:")
                for error in result.errors:
                    print(f"    - {error}")
            
            # حالة بعد الاستيراد
            after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            print(f"📊 عدد العناصر بعد الاستيراد: {after_count}")
            print(f"📊 الفرق: {after_count - before_count}")
            
            # فحص البيانات المستوردة
            imported_items = db_manager.fetch_all(
                "SELECT item_name, item_code, is_active FROM organizational_chart WHERE item_code LIKE 'EXCEL%' OR item_name LIKE '%Excel%'"
            )
            
            print(f"📋 البيانات المستوردة ({len(imported_items)}):")
            for item in imported_items:
                print(f"    - {item[0]} ({item[1]}) - نشط: {item[2]}")
            
            # تنظيف ملف Excel
            try:
                excel_file.unlink()
                print(f"🗑️ تم حذف ملف Excel المؤقت")
            except:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار استيراد Excel: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_activation_deactivation(self):
        """اختبار التفعيل والإلغاء"""
        print("\n🔄 اختبار التفعيل والإلغاء...")
        
        try:
            # إنشاء عنصر اختبار
            test_item = OrganizationalChart(
                sequence_number=999,
                item_code='ACTIVATE_TEST',
                item_name='اختبار التفعيل',
                unit='قطعة',
                quantity=1,
                is_active=True
            )
            
            if not test_item.save():
                print("❌ فشل في إنشاء عنصر الاختبار")
                return False
            
            print(f"✅ تم إنشاء عنصر الاختبار: {test_item.item_name}")
            
            # اختبار الإلغاء
            print("🔄 اختبار إلغاء التفعيل...")
            if test_item.delete():
                print("✅ تم إلغاء تفعيل العنصر")
                
                # التحقق من الحالة
                updated_item = OrganizationalChart.get_by_id(test_item.id)
                if updated_item and not updated_item.is_active:
                    print("✅ تأكيد: العنصر غير نشط في قاعدة البيانات")
                else:
                    print("❌ خطأ: العنصر لا يزال نشطاً")
            else:
                print("❌ فشل في إلغاء تفعيل العنصر")
            
            # اختبار إعادة التفعيل
            print("🔄 اختبار إعادة التفعيل...")
            test_item.is_active = True
            if test_item.save():
                print("✅ تم إعادة تفعيل العنصر")
                
                # التحقق من الحالة
                updated_item = OrganizationalChart.get_by_id(test_item.id)
                if updated_item and updated_item.is_active:
                    print("✅ تأكيد: العنصر نشط في قاعدة البيانات")
                else:
                    print("❌ خطأ: العنصر لا يزال غير نشط")
            else:
                print("❌ فشل في إعادة تفعيل العنصر")
            
            # تنظيف عنصر الاختبار
            db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code = 'ACTIVATE_TEST'")
            print("🗑️ تم حذف عنصر الاختبار")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التفعيل والإلغاء: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup_test_data(self):
        """تنظيف بيانات الاختبار"""
        print("\n🧹 تنظيف بيانات الاختبار...")
        
        try:
            # حذف بيانات الاختبار
            patterns = ['DIAG%', 'EXCEL%', 'ACTIVATE_TEST']
            total_deleted = 0
            
            for pattern in patterns:
                deleted = db_manager.execute_query(
                    "DELETE FROM organizational_chart WHERE item_code LIKE ?",
                    (pattern,)
                ).rowcount
                total_deleted += deleted
                print(f"🗑️ حذف {deleted} عنصر بنمط {pattern}")
            
            print(f"📊 إجمالي العناصر المحذوفة: {total_deleted}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تنظيف البيانات: {e}")
            return False
    
    def run_diagnostic(self):
        """تشغيل التشخيص الكامل"""
        print("🚀 بدء التشخيص الشامل...")
        print("=" * 50)
        
        tests = [
            ("فحص بنية قاعدة البيانات", self.check_database_structure),
            ("إنشاء بيانات اختبار", self.create_test_data),
            ("اختبار دالة get_all", self.test_get_all_function),
            ("اختبار استيراد Excel", self.test_excel_import),
            ("اختبار التفعيل والإلغاء", self.test_activation_deactivation),
            ("اختبار دالة delete_all", self.test_delete_all_function),
            ("تنظيف بيانات الاختبار", self.cleanup_test_data)
        ]
        
        successful_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_function():
                    successful_tests += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ غير متوقع: {e}")
            
            time.sleep(1)  # تأخير بين الاختبارات
        
        # النتائج النهائية
        print("\n" + "=" * 50)
        print("📊 نتائج التشخيص النهائية")
        print("=" * 50)
        
        success_rate = (successful_tests / total_tests) * 100
        print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        
        if success_rate >= 80:
            print("🎉 التشخيص ممتاز - النظام يعمل بشكل جيد")
        elif success_rate >= 60:
            print("⚠️ التشخيص جيد - توجد بعض المشاكل البسيطة")
        else:
            print("❌ التشخيص ضعيف - توجد مشاكل تحتاج إلى إصلاح")
        
        return success_rate >= 60

def main():
    """الدالة الرئيسية"""
    print("🔧 تشخيص مشاكل الاستيراد وحذف الأصناف")
    print("=" * 50)
    print("هذا التشخيص سيفحص:")
    print("1. بنية قاعدة البيانات")
    print("2. وظائف الاستيراد من Excel")
    print("3. وظائف حذف الأصناف")
    print("4. التفعيل والإلغاء")
    print("5. تناسق البيانات")
    print("=" * 50)
    
    diagnostic = ImportDeleteDiagnostic()
    success = diagnostic.run_diagnostic()
    
    if success:
        print("\n🎉 التشخيص اكتمل بنجاح!")
        return 0
    else:
        print("\n⚠️ التشخيص كشف عن مشاكل تحتاج إلى إصلاح")
        return 1

if __name__ == "__main__":
    sys.exit(main())