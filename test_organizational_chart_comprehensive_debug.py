#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لشاشة الجدول التنظيمي - تشخيص الأخطاء
Comprehensive Test for Organizational Chart Window - Debug Mode

هذا الاختبار يركز على:
1. اختبار عملية الاستيراد من Excel
2. اختبار عملية حذف جميع الأصناف
3. تشخيص الأخطاء وتتبع المشاكل
4. اختبار التكامل مع قاعدة البيانات
"""

import sys
import os
from pathlib import Path
import tkinter as tk
import ttkbootstrap as ttk_bs
import pandas as pd
import time
import traceback
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# الاستيرادات المطلوبة
try:
    from database import db_manager
    from models import OrganizationalChart
    from ui.organizational_chart_window import OrganizationalChartWindow
    from utils.organizational_chart_import import import_organizational_chart_from_excel
    print("✅ تم استيراد جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class OrganizationalChartDebugTester:
    """فئة اختبار شاشة الجدول التنظيمي مع تشخيص الأخطاء"""
    
    def __init__(self):
        self.test_results = []
        self.test_data_file = None
        self.window = None
        self.org_chart_window = None
        
        print("🔧 بدء اختبار شاشة الجدول التنظيمي - وضع التشخيص")
        print("=" * 60)
    
    def log_test(self, test_name: str, success: bool, message: str = "", details: str = ""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        }
        self.test_results.append(result)
        
        print(f"{status} [{result['timestamp']}] {test_name}")
        if message:
            print(f"    📝 {message}")
        if details:
            print(f"    🔍 {details}")
        print()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        try:
            print("🔧 إعداد بيئة الاختبار...")
            
            # إنشاء النافذة الرئيسية
            self.window = ttk_bs.Window(
                title="اختبار الجدول التنظيمي - وضع التشخيص",
                themename="cosmo",
                size=(1200, 800)
            )
            
            # إخفاء النافذة الرئيسية
            self.window.withdraw()
            
            self.log_test("إعداد بيئة الاختبار", True, "تم إنشاء النافذة الرئيسية")
            return True
            
        except Exception as e:
            self.log_test("إعداد بيئة الاختبار", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            print("🔍 اختبار الاتصال بقاعدة البيانات...")
            
            # اختبار الاتصال الأساسي
            result = db_manager.fetch_one("SELECT 1")
            if not result:
                raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            # اختبار وجود جدول الجدول التنظيمي
            try:
                count_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")
                current_count = count_result[0] if count_result else 0
                
                self.log_test("اختبار قاعدة البيانات", True, 
                            f"الاتصال ناجح، عدد العناصر الحالي: {current_count}")
                return True
                
            except Exception as table_error:
                # إنشاء الجدول إذا لم يكن موجوداً
                print("⚠️ جدول الجدول التنظيمي غير موجود، سيتم إنشاؤه...")
                OrganizationalChart.create_table()
                
                self.log_test("اختبار قاعدة البيانات", True, 
                            "تم إنشاء جدول الجدول التنظيمي", str(table_error))
                return True
                
        except Exception as e:
            self.log_test("اختبار قاعدة البيانات", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def create_test_excel_file(self):
        """إنشاء ملف Excel للاختبار"""
        try:
            print("📄 إنشاء ملف Excel للاختبار...")
            
            # بيانات الاختبار
            test_data = [
                {
                    'التسلسل': 1,
                    'رقم الصنف': 'TEST001',
                    'اسم الصنف': 'صنف اختبار 1',
                    'اسم المعدة': 'معدة اختبار 1',
                    'الكمية': 10,
                    'الملاحظات': 'ملاحظة اختبار 1'
                },
                {
                    'التسلسل': 2,
                    'رقم الصنف': 'TEST002',
                    'اسم الصنف': 'صنف اختبار 2',
                    'اسم المعدة': 'معدة اختبار 2',
                    'الكمية': 20,
                    'الملاحظات': 'ملاحظة اختبار 2'
                },
                {
                    'التسلسل': 3,
                    'رقم الصنف': 'TEST003',
                    'اسم الصنف': 'صنف اختبار 3',
                    'اسم المعدة': 'معدة اختبار 3',
                    'الكمية': 30,
                    'الملاحظات': 'ملاحظة اختبار 3'
                },
                {
                    'التسلسل': 4,
                    'رقم الصنف': '',  # بدون رقم صنف
                    'اسم الصنف': 'صنف اختبار بدون رقم',
                    'اسم المعدة': 'معدة اختبار 4',
                    'الكمية': 5,
                    'الملاحظات': 'اختبار بدون رقم صنف'
                },
                {
                    'التسلسل': 5,
                    'رقم الصنف': 'TEST001',  # رقم مكرر لاختبار التعامل مع التكرار
                    'اسم الصنف': 'صنف اختبار مكرر',
                    'اسم المعدة': 'معدة اختبار 5',
                    'الكمية': 15,
                    'الملاحظات': 'اختبار رقم صنف مكرر'
                }
            ]
            
            # إنشاء DataFrame
            df = pd.DataFrame(test_data)
            
            # حفظ الملف
            self.test_data_file = project_root / "test_organizational_chart_data.xlsx"
            df.to_excel(self.test_data_file, index=False, engine='openpyxl')
            
            self.log_test("إنشاء ملف Excel", True, 
                        f"تم إنشاء ملف الاختبار: {self.test_data_file}",
                        f"عدد الصفوف: {len(test_data)}")
            return True
            
        except Exception as e:
            self.log_test("إنشاء ملف Excel", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_organizational_chart_window_creation(self):
        """اختبار إنشاء شاشة الجدول التنظيمي"""
        try:
            print("🖼️ اختبار إنشاء شاشة الجدول التنظيمي...")
            
            # إنشاء شاشة الجدول التنظيمي
            self.org_chart_window = OrganizationalChartWindow(self.window, None)
            
            # التحقق من إنشاء العناصر الأساسية
            if not hasattr(self.org_chart_window, 'window'):
                raise Exception("لم يتم إنشاء النافذة")
            
            if not hasattr(self.org_chart_window, 'tree'):
                raise Exception("لم يتم إنشاء جدول البيانات")
            
            # إخفاء النافذة لتجنب التداخل
            self.org_chart_window.window.withdraw()
            
            self.log_test("إنشاء شاشة الجدول التنظيمي", True, 
                        "تم إنشاء الشاشة وجميع عناصرها بنجاح")
            return True
            
        except Exception as e:
            self.log_test("إنشاء شاشة الجدول التنظيمي", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_data_loading(self):
        """اختبار تحميل البيانات"""
        try:
            print("📊 اختبار تحميل البيانات...")
            
            if not self.org_chart_window:
                raise Exception("شاشة الجدول التنظيمي غير متاحة")
            
            # تحميل البيانات
            self.org_chart_window.load_data()
            
            # التحقق من تحميل البيانات
            items_count = len(getattr(self.org_chart_window, 'items_data', []))
            tree_items_count = len(self.org_chart_window.tree.get_children())
            
            self.log_test("تحميل البيانات", True, 
                        f"تم تحميل البيانات بنجاح",
                        f"عدد العناصر في البيانات: {items_count}, في الجدول: {tree_items_count}")
            return True
            
        except Exception as e:
            self.log_test("تحميل البيانات", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_excel_import_function(self):
        """اختبار دالة الاستيراد من Excel"""
        try:
            print("📥 اختبار دالة الاستيراد من Excel...")
            
            if not self.test_data_file or not self.test_data_file.exists():
                raise Exception("ملف البيانات غير متاح")
            
            # تسجيل حالة قاعدة البيانات قبل الاستيراد
            before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            print(f"📊 عدد العناصر قبل الاستيراد: {before_count}")
            
            # تشغيل دالة الاستيراد
            result = import_organizational_chart_from_excel(str(self.test_data_file))
            
            # تسجيل حالة قاعدة البيانات بعد الاستيراد
            after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            print(f"📊 عدد العناصر بعد الاستيراد: {after_count}")
            
            # التحقق من النتائج
            if not result:
                raise Exception("دالة الاستيراد لم ترجع نتيجة")
            
            success_message = (f"نجح: {result.success_count}, "
                             f"مكرر: {result.duplicate_count}, "
                             f"خطأ: {result.error_count}")
            
            details = (f"قبل الاستيراد: {before_count}, "
                      f"بعد الاستيراد: {after_count}, "
                      f"الفرق: {after_count - before_count}")
            
            self.log_test("دالة الاستيراد من Excel", True, success_message, details)
            
            # طباعة تفاصيل إضافية
            if hasattr(result, 'errors') and result.errors:
                print("⚠️ أخطاء الاستيراد:")
                for error in result.errors[:5]:  # أول 5 أخطاء فقط
                    print(f"    - {error}")
            
            return True
            
        except Exception as e:
            self.log_test("دالة الاستيراد من Excel", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_import_through_ui(self):
        """اختبار الاستيراد من خلال واجهة المستخدم"""
        try:
            print("🖱️ اختبار الاستيراد من خلال واجهة المستخدم...")
            
            if not self.org_chart_window:
                raise Exception("شاشة الجدول التنظيمي غير متاحة")
            
            if not self.test_data_file or not self.test_data_file.exists():
                raise Exception("ملف البيانات غير متاح")
            
            # تسجيل حالة البيانات قبل الاستيراد
            before_items = len(getattr(self.org_chart_window, 'items_data', []))
            before_tree = len(self.org_chart_window.tree.get_children())
            
            print(f"📊 قبل الاستيراد - البيانات: {before_items}, الجدول: {before_tree}")
            
            # محاكاة عملية الاستيراد (بدون فتح نافذة اختيار الملف)
            # سنستخدم الدالة المباشرة بدلاً من واجهة المستخدم
            
            # تحديث البيانات بعد الاستيراد
            self.org_chart_window.load_data()
            
            # تسجيل حالة البيانات بعد الاستيراد
            after_items = len(getattr(self.org_chart_window, 'items_data', []))
            after_tree = len(self.org_chart_window.tree.get_children())
            
            print(f"📊 بعد الاستيراد - البيانات: {after_items}, الجدول: {after_tree}")
            
            details = (f"قبل: البيانات={before_items}, الجدول={before_tree} | "
                      f"بعد: البيانات={after_items}, الجدول={after_tree}")
            
            self.log_test("الاستيراد من خلال واجهة المستخدم", True, 
                        "تم تحديث البيانات في الواجهة", details)
            return True
            
        except Exception as e:
            self.log_test("الاستيراد من خلال واجهة المستخدم", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_delete_all_function(self):
        """اختبار دالة حذف جميع الأصناف"""
        try:
            print("🗑️ اختبار دالة حذف جميع الأصناف...")
            
            # تسجيل حالة قاعدة البيانات قبل الحذف
            before_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            before_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            print(f"📊 قبل الحذف - نشط: {before_active}, إجمالي: {before_total}")
            
            # تشغيل دالة الحذف
            delete_result = OrganizationalChart.delete_all()
            
            # تسجيل حالة قاعدة البيانات بعد الحذف
            after_active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            after_total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            print(f"📊 بعد الحذف - نشط: {after_active}, إجمالي: {after_total}")
            
            # تحليل النتائج
            deleted_count = before_active - after_active
            
            success_message = f"نتيجة الحذف: {delete_result}, تم حذف {deleted_count} عنصر"
            details = (f"قبل الحذف: نشط={before_active}, إجمالي={before_total} | "
                      f"بعد الحذف: نشط={after_active}, إجمالي={after_total}")
            
            # تحديد نجاح الاختبار
            test_success = delete_result is not False  # نجح إذا لم يكن False
            
            self.log_test("دالة حذف جميع الأصناف", test_success, success_message, details)
            return test_success
            
        except Exception as e:
            self.log_test("دالة حذف جميع الأصناف", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_delete_all_through_ui(self):
        """اختبار حذف جميع الأصناف من خلال واجهة المستخدم"""
        try:
            print("🖱️ اختبار حذف جميع الأصناف من خلال واجهة المستخدم...")
            
            if not self.org_chart_window:
                raise Exception("شاشة الجدول التنظيمي غير متاحة")
            
            # تسجيل حالة البيانات قبل الحذف
            before_items = len(getattr(self.org_chart_window, 'items_data', []))
            before_tree = len(self.org_chart_window.tree.get_children())
            
            print(f"📊 قبل الحذف - البيانات: {before_items}, الجدول: {before_tree}")
            
            # محاكاة عملية الحذف (بدون تأكيد المستخدم)
            # سنستخدم الدالة المباشرة
            
            # تحديث البيانات بعد الحذف
            self.org_chart_window.load_data()
            
            # تسجيل حالة البيانات بعد الحذف
            after_items = len(getattr(self.org_chart_window, 'items_data', []))
            after_tree = len(self.org_chart_window.tree.get_children())
            
            print(f"📊 بعد الحذف - البيانات: {after_items}, الجدول: {after_tree}")
            
            details = (f"قبل: البيانات={before_items}, الجدول={before_tree} | "
                      f"بعد: البيانات={after_items}, الجدول={after_tree}")
            
            self.log_test("حذف جميع الأصناف من خلال واجهة المستخدم", True, 
                        "تم تحديث البيانات في الواجهة بعد الحذف", details)
            return True
            
        except Exception as e:
            self.log_test("حذف جميع الأصناف من خلال واجهة المستخدم", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def test_data_consistency(self):
        """اختبار تناسق البيانات"""
        try:
            print("🔍 اختبار تناسق البيانات...")
            
            # فحص تناسق قاعدة البيانات
            db_active_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
            db_total_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
            
            # فحص تناسق النموذج
            model_items = OrganizationalChart.get_all(active_only=True)
            model_active_count = len(model_items)
            
            # فحص تناسق واجهة المستخدم
            ui_items_count = 0
            ui_tree_count = 0
            
            if self.org_chart_window:
                ui_items_count = len(getattr(self.org_chart_window, 'items_data', []))
                ui_tree_count = len(self.org_chart_window.tree.get_children())
            
            # تحليل التناسق
            consistency_issues = []
            
            if db_active_count != model_active_count:
                consistency_issues.append(f"عدم تطابق بين قاعدة البيانات ({db_active_count}) والنموذج ({model_active_count})")
            
            if self.org_chart_window and ui_items_count != db_active_count:
                consistency_issues.append(f"عدم تطابق بين قاعدة البيانات ({db_active_count}) وبيانات الواجهة ({ui_items_count})")
            
            if self.org_chart_window and ui_tree_count != ui_items_count:
                consistency_issues.append(f"عدم تطابق بين بيانات الواجهة ({ui_items_count}) وجدول الواجهة ({ui_tree_count})")
            
            # النتيجة
            is_consistent = len(consistency_issues) == 0
            
            details = (f"قاعدة البيانات: نشط={db_active_count}, إجمالي={db_total_count} | "
                      f"النموذج: {model_active_count} | "
                      f"الواجهة: بيانات={ui_items_count}, جدول={ui_tree_count}")
            
            if consistency_issues:
                message = f"وجدت {len(consistency_issues)} مشكلة في التناسق"
                details += f" | المشاكل: {'; '.join(consistency_issues)}"
            else:
                message = "البيانات متناسقة في جميع الطبقات"
            
            self.log_test("تناسق البيانات", is_consistent, message, details)
            return is_consistent
            
        except Exception as e:
            self.log_test("تناسق البيانات", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def cleanup_test_data(self):
        """تنظيف بيانات الاختبار"""
        try:
            print("🧹 تنظيف بيانات الاختبار...")
            
            # حذف ملف Excel المؤقت
            if self.test_data_file and self.test_data_file.exists():
                self.test_data_file.unlink()
                print(f"🗑️ تم حذف ملف الاختبار: {self.test_data_file}")
            
            # حذف بيانات الاختبار من قاعدة البيانات
            deleted_count = db_manager.execute_query(
                "DELETE FROM organizational_chart WHERE item_code LIKE 'TEST%'"
            ).rowcount
            
            print(f"🗑️ تم حذف {deleted_count} عنصر اختبار من قاعدة البيانات")
            
            self.log_test("تنظيف بيانات الاختبار", True, 
                        f"تم تنظيف {deleted_count} عنصر اختبار")
            return True
            
        except Exception as e:
            self.log_test("تنظيف بيانات الاختبار", False, f"خطأ: {e}", traceback.format_exc())
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء تشغيل جميع الاختبارات...")
        print("=" * 60)
        
        tests = [
            ("إعداد بيئة الاختبار", self.setup_test_environment),
            ("اختبار قاعدة البيانات", self.test_database_connection),
            ("إنشاء ملف Excel", self.create_test_excel_file),
            ("إنشاء شاشة الجدول التنظيمي", self.test_organizational_chart_window_creation),
            ("تحميل البيانات", self.test_data_loading),
            ("دالة الاستيراد من Excel", self.test_excel_import_function),
            ("الاستيراد من خلال واجهة المستخدم", self.test_import_through_ui),
            ("دالة حذف جميع الأصناف", self.test_delete_all_function),
            ("حذف جميع الأصناف من خلال واجهة المستخدم", self.test_delete_all_through_ui),
            ("تناسق البيانات", self.test_data_consistency),
            ("تنظيف بيانات الاختبار", self.cleanup_test_data)
        ]
        
        successful_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            try:
                if test_function():
                    successful_tests += 1
                time.sleep(0.5)  # تأخير قصير بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ غير متوقع في {test_name}: {e}")
        
        # تقرير النتائج النهائي
        print("=" * 60)
        print("📊 تقرير النتائج النهائي")
        print("=" * 60)
        
        success_rate = (successful_tests / total_tests) * 100
        print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print()
        
        # تفاصيل النتائج
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} [{result['timestamp']}] {result['test']}")
            if result['message']:
                print(f"    📝 {result['message']}")
            if not result['success'] and result['details']:
                print(f"    🔍 {result['details'][:200]}...")
        
        print("=" * 60)
        
        # تنظيف الموارد
        try:
            if self.org_chart_window and hasattr(self.org_chart_window, 'window'):
                self.org_chart_window.window.destroy()
            if self.window:
                self.window.destroy()
        except:
            pass
        
        return success_rate >= 70  # نجح إذا كان معدل النجاح 70% أو أكثر

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار شامل لشاشة الجدول التنظيمي - وضع التشخيص")
    print("=" * 60)
    print("هذا الاختبار سيقوم بـ:")
    print("1. اختبار الاتصال بقاعدة البيانات")
    print("2. إنشاء ملف Excel للاختبار")
    print("3. اختبار عملية الاستيراد من Excel")
    print("4. اختبار عملية حذف جميع الأصناف")
    print("5. فحص تناسق البيانات")
    print("6. تشخيص الأخطاء المحتملة")
    print("=" * 60)
    
    # تشغيل الاختبار
    tester = OrganizationalChartDebugTester()
    success = tester.run_all_tests()
    
    if success:
        print("🎉 اكتملت جميع الاختبارات بنجاح!")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت - راجع التفاصيل أعلاه")
        return 1

if __name__ == "__main__":
    sys.exit(main())