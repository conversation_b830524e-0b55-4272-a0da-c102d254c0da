#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لمشاكل الجدول التنظيمي - استيراد الإكسل وحذف الأصناف
"""

import sys
import os
import pandas as pd
import tempfile
import traceback

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart


def print_separator(title=""):
    """طباعة فاصل مع عنوان"""
    print("\n" + "="*70)
    if title:
        print(f"🔍 {title}")
        print("="*70)


def check_database_connection():
    """فحص اتصال قاعدة البيانات"""
    try:
        result = db_manager.fetch_one("SELECT 1")
        return result is not None
    except Exception as e:
        print(f"❌ خطأ في اتصال قاعدة البيانات: {e}")
        return False


def get_database_stats():
    """الحصول على إحصائيات قاعدة البيانات"""
    try:
        total = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")[0]
        active = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        inactive = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 0")[0]
        
        return {
            'total': total,
            'active': active,
            'inactive': inactive
        }
    except Exception as e:
        print(f"❌ خطأ في الحصول على الإحصائيات: {e}")
        return None


def test_delete_all_function():
    """اختبار دالة حذف جميع الأصناف"""
    print_separator("اختبار دالة حذف جميع الأصناف")
    
    try:
        # إنشاء بيانات تجريبية
        print("📝 إنشاء بيانات تجريبية...")
        test_items = []
        
        for i in range(1, 6):
            item = OrganizationalChart(
                sequence_number=8000 + i,
                item_code=f"DEL_TEST_{i:03d}",
                item_name=f"صنف حذف تجريبي {i}",
                unit="قطعة",
                quantity=10.0,
                notes=f"اختبار حذف {i}",
                is_active=True
            )
            
            if item.save():
                test_items.append(item)
                print(f"   ✅ تم إنشاء: {item.item_name} (ID: {item.id})")
            else:
                print(f"   ❌ فشل في إنشاء: صنف حذف تجريبي {i}")
        
        if not test_items:
            print("❌ فشل في إنشاء البيانات التجريبية")
            return False
        
        # فحص الحالة قبل الحذف
        stats_before = get_database_stats()
        print(f"\n📊 الحالة قبل الحذف:")
        print(f"   📦 إجمالي: {stats_before['total']}")
        print(f"   ✅ نشط: {stats_before['active']}")
        print(f"   ❌ غير نشط: {stats_before['inactive']}")
        
        # تنفيذ حذف جميع الأصناف
        print("\n🗑️ تنفيذ حذف جميع الأصناف...")
        delete_result = OrganizationalChart.delete_all()
        print(f"📊 نتيجة الحذف: {delete_result}")
        
        # فحص الحالة بعد الحذف
        stats_after = get_database_stats()
        print(f"\n📊 الحالة بعد الحذف:")
        print(f"   📦 إجمالي: {stats_after['total']}")
        print(f"   ✅ نشط: {stats_after['active']}")
        print(f"   ❌ غير نشط: {stats_after['inactive']}")
        
        # فحص البيانات التجريبية المحددة
        remaining_test = db_manager.fetch_all("""
            SELECT id, item_name, item_code, is_active 
            FROM organizational_chart 
            WHERE item_code LIKE 'DEL_TEST_%'
        """)
        
        print(f"\n🔍 البيانات التجريبية المتبقية: {len(remaining_test)}")
        for item in remaining_test:
            status = "نشط" if item['is_active'] else "غير نشط"
            print(f"   • {item['item_name']} ({item['item_code']}) - {status}")
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'DEL_TEST_%'")
        print("🧹 تم تنظيف البيانات التجريبية")
        
        return delete_result
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحذف: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False


def test_excel_import():
    """اختبار استيراد الإكسل"""
    print_separator("اختبار استيراد الإكسل")
    
    try:
        # إنشاء ملف Excel تجريبي
        print("📁 إنشاء ملف Excel تجريبي...")
        test_data = {
            'اسم الصنف': [
                'صنف استيراد تجريبي 1',
                'صنف استيراد تجريبي 2', 
                'صنف استيراد تجريبي 3',
                'صنف استيراد تجريبي 4',
                'صنف استيراد تجريبي 5'
            ],
            'رقم الصنف': ['IMP_TEST_001', 'IMP_TEST_002', 'IMP_TEST_003', 'IMP_TEST_004', 'IMP_TEST_005'],
            'اسم المعدة': ['معدة 1', 'معدة 2', 'معدة 3', 'معدة 4', 'معدة 5'],
            'الكمية': [10, 20, 30, 40, 50],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3', 'ملاحظة 4', 'ملاحظة 5']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف: {temp_file_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'IMP_TEST_%'")
        
        # فحص الحالة قبل الاستيراد
        stats_before = get_database_stats()
        print(f"\n📊 الحالة قبل الاستيراد:")
        print(f"   📦 إجمالي: {stats_before['total']}")
        print(f"   ✅ نشط: {stats_before['active']}")
        
        # استيراد البيانات
        print("\n📥 بدء استيراد البيانات...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors[:5]:  # عرض أول 5 أخطاء
                print(f"      • {error}")
        
        # فحص الحالة بعد الاستيراد
        stats_after = get_database_stats()
        print(f"\n📊 الحالة بعد الاستيراد:")
        print(f"   📦 إجمالي: {stats_after['total']}")
        print(f"   ✅ نشط: {stats_after['active']}")
        
        # فحص البيانات المستوردة
        imported_items = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, item_code, unit, is_active, created_at 
            FROM organizational_chart 
            WHERE item_code LIKE 'IMP_TEST_%'
            ORDER BY item_code
        """)
        
        print(f"\n📋 البيانات المستوردة ({len(imported_items)} عنصر):")
        for item in imported_items:
            status = "نشط" if item['is_active'] else "غير نشط"
            print(f"   • {item['item_name']} ({item['item_code']}) - {status}")
            print(f"     التسلسل: {item['sequence_number']}, المعدة: {item['unit']}")
        
        # اختبار عرض البيانات من خلال النموذج
        print("\n🔍 اختبار عرض البيانات من خلال النموذج:")
        all_items = OrganizationalChart.get_all(active_only=True)
        test_items_from_model = [item for item in all_items if item.item_code and item.item_code.startswith('IMP_TEST_')]
        
        print(f"   📦 إجمالي العناصر النشطة من النموذج: {len(all_items)}")
        print(f"   🧪 العناصر التجريبية من النموذج: {len(test_items_from_model)}")
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'IMP_TEST_%'")
        
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        print("🧹 تم تنظيف البيانات التجريبية")
        
        return result.success_count > 0 and len(test_items_from_model) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False


def main():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل لمشاكل الجدول التنظيمي")
    print("="*70)
    
    # فحص اتصال قاعدة البيانات
    if not check_database_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return False
    
    print("✅ تم الاتصال بقاعدة البيانات بنجاح")
    
    # فحص الحالة الأولية
    initial_stats = get_database_stats()
    if initial_stats:
        print(f"\n📊 الحالة الأولية:")
        print(f"   📦 إجمالي: {initial_stats['total']}")
        print(f"   ✅ نشط: {initial_stats['active']}")
        print(f"   ❌ غير نشط: {initial_stats['inactive']}")
    
    # تشغيل الاختبارات
    results = {}
    
    # اختبار حذف جميع الأصناف
    results['delete_all'] = test_delete_all_function()
    
    # اختبار استيراد الإكسل
    results['excel_import'] = test_excel_import()
    
    # عرض النتائج النهائية
    print_separator("النتائج النهائية")
    
    print("📋 ملخص نتائج الاختبارات:")
    print(f"   🗑️ اختبار حذف جميع الأصناف: {'✅ نجح' if results['delete_all'] else '❌ فشل'}")
    print(f"   📥 اختبار استيراد الإكسل: {'✅ نجح' if results['excel_import'] else '❌ فشل'}")
    
    final_stats = get_database_stats()
    if final_stats:
        print(f"\n📊 الحالة النهائية:")
        print(f"   📦 إجمالي: {final_stats['total']}")
        print(f"   ✅ نشط: {final_stats['active']}")
        print(f"   ❌ غير نشط: {final_stats['inactive']}")
    
    # تحديد المشاكل
    if not results['delete_all']:
        print("\n⚠️ مشكلة في وظيفة حذف جميع الأصناف")
    
    if not results['excel_import']:
        print("\n⚠️ مشكلة في استيراد الإكسل")
    
    if all(results.values()):
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
    
    return all(results.values())


if __name__ == "__main__":
    main()
