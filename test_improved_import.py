#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التحسينات الجديدة لاستيراد الأصناف
Test for Improved Items Import Features

يختبر:
1. تصفية الأصناف ذات الكمية 0 أو الفارغة
2. منع استيراد الأصناف المكررة
3. رسائل النتائج المحسنة
"""

import sys
import os
from pathlib import Path
import pandas as pd
import time

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from database import db_manager
    from models import AddedItem
    from utils.excel_import_manager import ExcelImportManager
    print("✅ تم استيراد جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def create_test_excel_with_zero_quantities():
    """إنشاء ملف Excel يحتوي على أصناف بكميات مختلفة"""
    print("📄 إنشاء ملف Excel للاختبار...")
    
    test_data = [
        {
            'اسم الصنف': 'صنف بكمية صحيحة',
            'رقم الصنف': 'TEST001',
            'الكمية': 10,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف بكمية صفر',
            'رقم الصنف': 'TEST002',
            'الكمية': 0,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف بكمية فارغة',
            'رقم الصنف': 'TEST003',
            'الكمية': None,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف بكمية سالبة',
            'رقم الصنف': 'TEST004',
            'الكمية': -5,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف آخر بكمية صحيحة',
            'رقم الصنف': 'TEST005',
            'الكمية': 25,
            'الوحدة': 'كيلو',
            'نوع العهدة': 'عينية',
            'التصنيف': 'مواد'
        },
        {
            'اسم الصنف': 'صنف بكمية عشرية',
            'رقم الصنف': 'TEST006',
            'الكمية': 15.5,
            'الوحدة': 'متر',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أدوات'
        }
    ]
    
    df = pd.DataFrame(test_data)
    excel_file = project_root / "test_import_with_zeros.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel: {excel_file}")
    print(f"📊 عدد الصفوف: {len(test_data)}")
    print("📋 البيانات:")
    for i, item in enumerate(test_data, 1):
        quantity = item['الكمية'] if item['الكمية'] is not None else "فارغة"
        print(f"  {i}. {item['اسم الصنف']} - الكمية: {quantity}")
    
    return excel_file

def create_duplicate_test_excel():
    """إنشاء ملف Excel يحتوي على أصناف مكررة"""
    print("\n📄 إنشاء ملف Excel للاختبار (أصناف مكررة)...")
    
    test_data = [
        {
            'اسم الصنف': 'صنف جديد',
            'رقم الصنف': 'NEW001',
            'الكمية': 10,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف مكرر',
            'رقم الصنف': 'TEST001',  # نفس رقم الصنف من الاختبار السابق
            'الكمية': 20,
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة'
        },
        {
            'اسم الصنف': 'صنف آخر جديد',
            'رقم الصنف': 'NEW002',
            'الكمية': 15,
            'الوحدة': 'كيلو',
            'نوع العهدة': 'عينية',
            'التصنيف': 'مواد'
        }
    ]
    
    df = pd.DataFrame(test_data)
    excel_file = project_root / "test_import_duplicates.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel: {excel_file}")
    print(f"📊 عدد الصفوف: {len(test_data)}")
    
    return excel_file

def test_zero_quantity_filtering():
    """اختبار تصفية الأصناف ذات الكمية 0 أو الفارغة"""
    print("\n" + "="*60)
    print("🧪 اختبار تصفية الأصناف ذات الكمية 0 أو الفارغة")
    print("="*60)
    
    # إنشاء ملف الاختبار
    excel_file = create_test_excel_with_zero_quantities()
    
    # تشغيل الاستيراد
    print("\n🔄 تشغيل عملية الاستيراد...")
    result = ExcelImportManager.import_items_from_excel(str(excel_file))
    
    # عرض النتائج
    print("\n📊 نتائج الاختبار:")
    print(f"✅ تم استيراد: {result.success_count} صنف")
    print(f"⚠️ تم تخطي (مكرر): {result.duplicate_count} صنف")
    print(f"❌ أخطاء: {result.error_count} صنف")
    print(f"⏱️ وقت المعالجة: {result.processing_time:.2f} ثانية")
    
    print("\n📋 ملخص النتائج:")
    print(result.get_summary())
    
    # التحقق من النتائج المتوقعة
    expected_success = 3  # فقط الأصناف ذات الكمية الموجبة
    if result.success_count == expected_success:
        print(f"✅ النتيجة صحيحة: تم استيراد {expected_success} أصناف فقط (تم تصفية الأصناف ذات الكمية 0 أو الفارغة)")
        test_passed = True
    else:
        print(f"❌ النتيجة خاطئة: متوقع {expected_success} لكن تم استيراد {result.success_count}")
        test_passed = False
    
    # تنظيف الملف
    if excel_file.exists():
        excel_file.unlink()
        print("🗑️ تم حذف ملف الاختبار")
    
    return test_passed

def test_duplicate_prevention():
    """اختبار منع استيراد الأصناف المكررة"""
    print("\n" + "="*60)
    print("🧪 اختبار منع استيراد الأصناف المكررة")
    print("="*60)
    
    # إنشاء ملف الاختبار
    excel_file = create_duplicate_test_excel()
    
    # تشغيل الاستيراد
    print("\n🔄 تشغيل عملية الاستيراد...")
    result = ExcelImportManager.import_items_from_excel(str(excel_file))
    
    # عرض النتائج
    print("\n📊 نتائج الاختبار:")
    print(f"✅ تم استيراد: {result.success_count} صنف")
    print(f"⚠️ تم تخطي (مكرر): {result.duplicate_count} صنف")
    print(f"❌ أخطاء: {result.error_count} صنف")
    print(f"⏱️ وقت المعالجة: {result.processing_time:.2f} ثانية")
    
    print("\n📋 ملخص النتائج:")
    print(result.get_summary())
    
    # التحقق من النتائج المتوقعة
    expected_success = 2  # الأصناف الجديدة فقط
    expected_duplicates = 1  # الصنف المكرر
    
    if result.success_count == expected_success and result.duplicate_count == expected_duplicates:
        print(f"✅ النتيجة صحيحة: تم استيراد {expected_success} أصناف جديدة وتخطي {expected_duplicates} مكرر")
        test_passed = True
    else:
        print(f"❌ النتيجة خاطئة: متوقع {expected_success} جديد و {expected_duplicates} مكرر")
        print(f"   لكن النتيجة: {result.success_count} جديد و {result.duplicate_count} مكرر")
        test_passed = False
    
    # تنظيف الملف
    if excel_file.exists():
        excel_file.unlink()
        print("🗑️ تم حذف ملف الاختبار")
    
    return test_passed

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    print("\n🧹 تنظيف بيانات الاختبار...")
    
    try:
        # حذف الأصناف المضافة في الاختبار
        deleted_count = db_manager.execute_query(
            "DELETE FROM added_items WHERE item_number LIKE 'TEST%' OR item_number LIKE 'NEW%'"
        ).rowcount
        
        if deleted_count > 0:
            print(f"🗑️ تم حذف {deleted_count} صنف اختبار من قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في التنظيف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار التحسينات الجديدة لاستيراد الأصناف")
    print("="*60)
    print("هذا الاختبار سيفحص:")
    print("1. تصفية الأصناف ذات الكمية 0 أو الفارغة")
    print("2. منع استيراد الأصناف المكررة")
    print("3. رسائل النتائج المحسنة")
    print("="*60)
    
    # تشغيل الاختبارات
    tests = [
        ("تصفية الأصناف ذات الكمية 0", test_zero_quantity_filtering),
        ("منع استيراد الأصناف المكررة", test_duplicate_prevention)
    ]
    
    successful = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                successful += 1
                print(f"\n✅ {test_name} - نجح")
            else:
                print(f"\n❌ {test_name} - فشل")
        except Exception as e:
            print(f"\n❌ {test_name} - خطأ: {e}")
            import traceback
            traceback.print_exc()
        
        time.sleep(1)
    
    # تنظيف البيانات
    cleanup_test_data()
    
    # النتائج النهائية
    print("\n" + "="*60)
    print("📊 ملخص النتائج")
    print("="*60)
    
    success_rate = (successful / total) * 100
    print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful}/{total})")
    
    if success_rate == 100:
        print("🎉 جميع التحسينات تعمل بشكل ممتاز!")
        print("\n✅ الميزات المحسنة:")
        print("• تصفية الأصناف ذات الكمية 0 أو الفارغة")
        print("• منع استيراد الأصناف المكررة حسب رقم الصنف")
        print("• رسائل نتائج واضحة ومفصلة")
        print("• معلومات شاملة عن عملية الاستيراد")
    elif success_rate >= 50:
        print("⚠️ بعض التحسينات تعمل - توجد مشاكل بسيطة")
    else:
        print("❌ التحسينات لا تعمل بشكل صحيح - تحتاج إلى مراجعة")
    
    return 0 if success_rate >= 80 else 1

if __name__ == "__main__":
    sys.exit(main())