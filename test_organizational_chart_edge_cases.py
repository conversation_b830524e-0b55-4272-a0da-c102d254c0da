#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحالات الحدية لمشاكل الجدول التنظيمي
"""

import sys
import os
import pandas as pd
import tempfile
import traceback

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart, AddedItem


def print_separator(title=""):
    """طباعة فاصل مع عنوان"""
    print("\n" + "="*70)
    if title:
        print(f"🔍 {title}")
        print("="*70)


def test_delete_with_inventory_protection():
    """اختبار حذف الأصناف مع حماية المخزون"""
    print_separator("اختبار حذف الأصناف مع حماية المخزون")
    
    try:
        # إنشاء صنف في الجدول التنظيمي
        org_item = OrganizationalChart(
            sequence_number=9001,
            item_code="PROTECT_TEST_001",
            item_name="صنف محمي من الحذف",
            unit="قطعة",
            quantity=50.0,
            notes="اختبار حماية الحذف",
            is_active=True
        )
        
        if not org_item.save():
            print("❌ فشل في إنشاء صنف الجدول التنظيمي")
            return False
        
        print(f"✅ تم إنشاء صنف الجدول التنظيمي: {org_item.item_name}")
        
        # إضافة الصنف إلى المخزون
        inventory_item = AddedItem(
            item_number="PROTECT_TEST_001",
            item_name="صنف محمي من الحذف",
            unit="قطعة",
            current_quantity=25.0,
            entered_quantity=25.0,
            notes="صنف في المخزون",
            user_id=None,
            is_active=True
        )
        
        if not inventory_item.save():
            print("❌ فشل في إضافة الصنف إلى المخزون")
            return False
        
        print(f"✅ تم إضافة الصنف إلى المخزون: {inventory_item.item_name}")
        
        # اختبار حذف الصنف المحمي
        print("\n🔍 اختبار حذف الصنف المحمي...")
        check_result = org_item.check_inventory_before_delete()
        
        print(f"📊 نتيجة فحص الحماية:")
        print(f"   يمكن الحذف: {check_result['can_delete']}")
        print(f"   السبب: {check_result['reason']}")
        
        if check_result['can_delete']:
            print("❌ خطأ: يجب أن يكون الصنف محمي من الحذف")
            return False
        else:
            print("✅ الصنف محمي من الحذف كما هو متوقع")
        
        # اختبار حذف جميع الأصناف مع وجود أصناف محمية
        print("\n🗑️ اختبار حذف جميع الأصناف مع وجود أصناف محمية...")
        
        # إضافة صنف غير محمي
        unprotected_item = OrganizationalChart(
            sequence_number=9002,
            item_code="UNPROTECT_TEST_001",
            item_name="صنف غير محمي",
            unit="قطعة",
            quantity=10.0,
            notes="اختبار عدم الحماية",
            is_active=True
        )
        
        if unprotected_item.save():
            print(f"✅ تم إنشاء صنف غير محمي: {unprotected_item.item_name}")
        
        # فحص الحالة قبل الحذف
        before_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"📊 عدد الأصناف النشطة قبل الحذف: {before_count}")
        
        # تنفيذ حذف جميع الأصناف
        delete_result = OrganizationalChart.delete_all()
        
        # فحص الحالة بعد الحذف
        after_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"📊 عدد الأصناف النشطة بعد الحذف: {after_count}")
        
        # التحقق من الأصناف المحمية
        protected_items = db_manager.fetch_all("""
            SELECT id, item_name, item_code, is_active 
            FROM organizational_chart 
            WHERE item_code IN ('PROTECT_TEST_001', 'UNPROTECT_TEST_001')
        """)
        
        print(f"\n📋 حالة الأصناف التجريبية بعد الحذف:")
        for item in protected_items:
            status = "نشط" if item['is_active'] else "غير نشط"
            protection = "محمي" if item['item_code'] == 'PROTECT_TEST_001' else "غير محمي"
            print(f"   • {item['item_name']} ({item['item_code']}) - {status} ({protection})")
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM added_items WHERE item_number LIKE '%TEST_%'")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE '%TEST_%'")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحماية: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False


def test_excel_import_edge_cases():
    """اختبار حالات حدية لاستيراد الإكسل"""
    print_separator("اختبار حالات حدية لاستيراد الإكسل")
    
    try:
        # اختبار 1: ملف Excel بأعمدة مفقودة
        print("📋 اختبار 1: ملف Excel بأعمدة مفقودة...")
        
        incomplete_data = {
            'اسم الصنف': ['صنف ناقص 1', 'صنف ناقص 2'],
            'رقم الصنف': ['INCOMPLETE_001', 'INCOMPLETE_002'],
            # عمود 'اسم المعدة' مفقود
            'الكمية': [10, 20]
            # عمود 'الملاحظات' مفقود
        }
        
        df_incomplete = pd.DataFrame(incomplete_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df_incomplete.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف ناقص: {temp_file_path}")
        
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        print(f"📊 نتائج استيراد الملف الناقص:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 الأخطاء:")
            for error in result.errors[:3]:
                print(f"      • {error}")
        
        # تنظيف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        # اختبار 2: ملف Excel ببيانات مكررة
        print("\n📋 اختبار 2: ملف Excel ببيانات مكررة...")
        
        duplicate_data = {
            'اسم الصنف': ['صنف مكرر', 'صنف مكرر', 'صنف فريد'],
            'رقم الصنف': ['DUP_001', 'DUP_001', 'UNIQUE_001'],
            'اسم المعدة': ['معدة 1', 'معدة 1', 'معدة 2'],
            'الكمية': [10, 15, 20],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3']
        }
        
        df_duplicate = pd.DataFrame(duplicate_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df_duplicate.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف مكرر: {temp_file_path}")
        
        # تنظيف البيانات السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'DUP_%' OR item_code LIKE 'UNIQUE_%'")
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        print(f"📊 نتائج استيراد الملف المكرر:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        # فحص البيانات المستوردة
        imported_items = db_manager.fetch_all("""
            SELECT item_name, item_code, COUNT(*) as count
            FROM organizational_chart 
            WHERE item_code LIKE 'DUP_%' OR item_code LIKE 'UNIQUE_%'
            GROUP BY item_code
            ORDER BY item_code
        """)
        
        print(f"📋 البيانات المستوردة:")
        for item in imported_items:
            print(f"   • {item['item_name']} ({item['item_code']}) - عدد: {item['count']}")
        
        # تنظيف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        # اختبار 3: ملف Excel ببيانات غير صحيحة
        print("\n📋 اختبار 3: ملف Excel ببيانات غير صحيحة...")
        
        invalid_data = {
            'اسم الصنف': ['', 'صنف صحيح', None, 'صنف آخر'],
            'رقم الصنف': ['', 'VALID_001', 'INVALID_002', ''],
            'اسم المعدة': ['معدة 1', 'معدة 2', 'معدة 3', 'معدة 4'],
            'الكمية': ['نص', 25, -10, 30],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3', 'ملاحظة 4']
        }
        
        df_invalid = pd.DataFrame(invalid_data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df_invalid.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف غير صحيح: {temp_file_path}")
        
        # تنظيف البيانات السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'VALID_%' OR item_code LIKE 'INVALID_%'")
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        print(f"📊 نتائج استيراد الملف غير الصحيح:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 الأخطاء:")
            for error in result.errors[:5]:
                print(f"      • {error}")
        
        # تنظيف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'DUP_%' OR item_code LIKE 'UNIQUE_%' OR item_code LIKE 'VALID_%' OR item_code LIKE 'INVALID_%' OR item_code LIKE 'INCOMPLETE_%'")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات الحدية: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False


def test_ui_integration():
    """اختبار التكامل مع واجهة المستخدم"""
    print_separator("اختبار التكامل مع واجهة المستخدم")
    
    try:
        # محاكاة استدعاء الدوال من واجهة المستخدم
        print("🖥️ محاكاة استدعاء دوال واجهة المستخدم...")
        
        # إنشاء بيانات تجريبية
        test_items = []
        for i in range(1, 4):
            item = OrganizationalChart(
                sequence_number=9100 + i,
                item_code=f"UI_TEST_{i:03d}",
                item_name=f"صنف واجهة مستخدم {i}",
                unit="قطعة",
                quantity=10.0,
                notes=f"اختبار واجهة {i}",
                is_active=True
            )
            
            if item.save():
                test_items.append(item)
                print(f"   ✅ تم إنشاء: {item.item_name}")
        
        # اختبار عرض البيانات
        print("\n📋 اختبار عرض البيانات...")
        all_items = OrganizationalChart.get_all(active_only=True)
        ui_test_items = [item for item in all_items if item.item_code and item.item_code.startswith('UI_TEST_')]
        
        print(f"   📦 إجمالي العناصر النشطة: {len(all_items)}")
        print(f"   🧪 عناصر اختبار الواجهة: {len(ui_test_items)}")
        
        # اختبار تحديث البيانات
        print("\n🔄 اختبار تحديث البيانات...")
        if ui_test_items:
            first_item = ui_test_items[0]
            original_name = first_item.item_name
            first_item.item_name = "صنف محدث"
            first_item.quantity = 25.0
            
            if first_item.save():
                print(f"   ✅ تم تحديث: {original_name} -> {first_item.item_name}")
            else:
                print(f"   ❌ فشل في تحديث: {original_name}")
        
        # اختبار حذف عنصر واحد
        print("\n🗑️ اختبار حذف عنصر واحد...")
        if len(ui_test_items) > 1:
            item_to_delete = ui_test_items[1]
            item_name = item_to_delete.item_name
            
            if item_to_delete.delete():
                print(f"   ✅ تم حذف: {item_name}")
            else:
                print(f"   ❌ فشل في حذف: {item_name}")
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'UI_TEST_%'")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False


def main():
    """تشغيل جميع الاختبارات الحدية"""
    print("🚀 بدء اختبار الحالات الحدية للجدول التنظيمي")
    print("="*70)
    
    results = {}
    
    # اختبار حماية الحذف
    results['delete_protection'] = test_delete_with_inventory_protection()
    
    # اختبار حالات حدية لاستيراد الإكسل
    results['excel_edge_cases'] = test_excel_import_edge_cases()
    
    # اختبار التكامل مع واجهة المستخدم
    results['ui_integration'] = test_ui_integration()
    
    # عرض النتائج النهائية
    print_separator("النتائج النهائية")
    
    print("📋 ملخص نتائج الاختبارات:")
    print(f"   🛡️ اختبار حماية الحذف: {'✅ نجح' if results['delete_protection'] else '❌ فشل'}")
    print(f"   📊 اختبار حالات حدية للإكسل: {'✅ نجح' if results['excel_edge_cases'] else '❌ فشل'}")
    print(f"   🖥️ اختبار التكامل مع الواجهة: {'✅ نجح' if results['ui_integration'] else '❌ فشل'}")
    
    if all(results.values()):
        print("\n🎉 جميع الاختبارات الحدية نجحت!")
        print("\n✅ الخلاصة: وظائف الجدول التنظيمي تعمل بشكل صحيح")
        print("   • استيراد الإكسل يعمل بشكل صحيح")
        print("   • حذف جميع الأصناف يعمل بشكل صحيح")
        print("   • حماية الأصناف المرتبطة بالمخزون تعمل")
        print("   • معالجة الأخطاء تعمل بشكل صحيح")
    else:
        print("\n❌ هناك مشاكل في بعض الوظائف")
        failed_tests = [test for test, result in results.items() if not result]
        print(f"   الاختبارات الفاشلة: {', '.join(failed_tests)}")
    
    return all(results.values())


if __name__ == "__main__":
    main()
