#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار لوحة التحكم بعد إصلاح detect_types
"""

import sys
import importlib

def test_dashboard_with_fresh_db():
    """اختبار لوحة التحكم مع إعادة تحميل قاعدة البيانات"""
    print("🧪 اختبار لوحة التحكم مع إعادة تحميل قاعدة البيانات...")
    
    try:
        # إعادة تحميل وحدة database لضمان استخدام الإعدادات الجديدة
        if 'database' in sys.modules:
            importlib.reload(sys.modules['database'])
        
        # استيراد db_manager الجديد
        from database import db_manager
        
        print("✅ تم إعادة تحميل قاعدة البيانات")
        
        # اختبار الاستعلام الذي كان يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح!")
        
        # فحص كل معاملة
        for i, trans in enumerate(transactions):
            print(f"\nمعاملة {i+1}:")
            print(f"  التاريخ: {repr(trans['transaction_date'])}")
            print(f"  الرقم: {repr(trans['transaction_number'])}")
            print(f"  المستفيد: {repr(trans['beneficiary_name'])}")
            print(f"  عدد الأصناف: {repr(trans['items_count'])}")
        
        # اختبار دالة refresh_dashboard_data
        print("\n🔄 اختبار دالة refresh_dashboard_data...")
        
        # استيراد النماذج المطلوبة
        from models import OrganizationalChart, Transaction, AddedItem, Beneficiary
        
        # حساب إحصائيات لوحة التحكم
        dashboard_data = {}
        
        # إجمالي أصناف الجدول التنظيمي
        total_items = db_manager.fetch_one("SELECT COUNT(*) as count FROM organizational_chart WHERE is_active = 1")
        dashboard_data["total_items"] = total_items["count"] if total_items else 0
        print(f"✅ إجمالي أصناف الجدول التنظيمي: {dashboard_data['total_items']}")
        
        # إجمالي المستفيدين
        total_beneficiaries = db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1")
        dashboard_data["total_beneficiaries"] = total_beneficiaries["count"] if total_beneficiaries else 0
        print(f"✅ إجمالي المستفيدين: {dashboard_data['total_beneficiaries']}")
        
        # إجمالي عمليات الصرف
        total_transactions = db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'مكتملة'")
        dashboard_data["total_transactions"] = total_transactions["count"] if total_transactions else 0
        print(f"✅ إجمالي عمليات الصرف: {dashboard_data['total_transactions']}")
        
        # الأصناف منخفضة المخزون
        low_stock_items = db_manager.fetch_all("""
            SELECT ai.item_name, ai.current_quantity, ai.minimum_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= ai.minimum_quantity
            AND ai.minimum_quantity > 0
        """)
        dashboard_data["low_stock_count"] = len(low_stock_items)
        dashboard_data["low_stock_items"] = low_stock_items
        print(f"✅ الأصناف منخفضة المخزون: {dashboard_data['low_stock_count']}")
        
        # الأصناف التي تحتاج تموين (كمية 5 أو أقل)
        supply_needed_items = db_manager.fetch_all("""
            SELECT ai.item_name, ai.current_quantity
            FROM added_items ai
            WHERE ai.is_active = 1 
            AND ai.current_quantity <= 5
        """)
        dashboard_data["supply_needed_count"] = len(supply_needed_items)
        print(f"✅ الأصناف التي تحتاج تموين: {dashboard_data['supply_needed_count']}")
        
        print(f"\n📊 ملخص إحصائيات لوحة التحكم:")
        print(f"  - إجمالي أصناف الجدول التنظيمي: {dashboard_data['total_items']}")
        print(f"  - إجمالي المستفيدين: {dashboard_data['total_beneficiaries']}")
        print(f"  - إجمالي عمليات الصرف: {dashboard_data['total_transactions']}")
        print(f"  - الأصناف منخفضة المخزون: {dashboard_data['low_stock_count']}")
        print(f"  - الأصناف التي تحتاج تموين: {dashboard_data['supply_needed_count']}")
        
        return True
    
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة التحكم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_real_activities():
    """اختبار دالة get_real_activities"""
    print("\n🧪 اختبار دالة get_real_activities...")
    
    try:
        from database import db_manager
        from datetime import datetime
        
        # تنفيذ نفس الاستعلام الموجود في get_real_activities
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام الأنشطة...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة")
        
        activities = []
        
        for trans in transactions:
            try:
                date_str = trans['transaction_date']
                
                # معالجة البيانات المعطوبة (نفس الكود المحدث في main_window.py)
                if date_str and isinstance(date_str, bytes):
                    try:
                        date_str = date_str.decode('utf-8')
                    except:
                        date_str = None
                
                if date_str and isinstance(date_str, str):
                    if date_str.startswith("b'") or date_str.startswith('b"'):
                        date_str = None
                    elif not date_str.replace('-', '').replace(':', '').replace(' ', '').replace('.', '').isdigit():
                        date_str = None
                
                if isinstance(date_str, str) and date_str:
                    try:
                        if '.' in date_str:
                            date_str = date_str.split('.')[0]
                        
                        if ' ' in date_str:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        
                        time_str = trans_date.strftime('%H:%M')
                        date_display = trans_date.strftime('%m/%d')
                    except (ValueError, TypeError) as e:
                        print(f"خطأ في معالجة التاريخ: {e} - التاريخ: {repr(date_str)}")
                        time_str = "00:00"
                        date_display = "اليوم"
                else:
                    time_str = "00:00"
                    date_display = "اليوم"

                beneficiary = trans['beneficiary_name'] or "غير محدد"
                try:
                    items_count = int(float(trans['items_count'])) if trans['items_count'] else 0
                except (ValueError, TypeError):
                    items_count = 0
                
                activity = {
                    'type': 'transaction',
                    'description': f"عملية صرف - {beneficiary}",
                    'details': f"{items_count} صنف",
                    'time': time_str,
                    'date': date_display
                }
                
                activities.append(activity)
                print(f"  ✅ نشاط: {activity['description']} - {activity['time']}")
            
            except Exception as e:
                print(f"  ❌ خطأ في معالجة المعاملة: {e}")
        
        print(f"✅ تم إنشاء {len(activities)} نشاط بنجاح")
        return activities
    
    except Exception as e:
        print(f"❌ خطأ في اختبار get_real_activities: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    print("🚀 اختبار لوحة التحكم بعد إصلاح detect_types")
    print("=" * 60)
    
    # اختبار لوحة التحكم
    dashboard_success = test_dashboard_with_fresh_db()
    
    # اختبار get_real_activities
    activities = test_get_real_activities()
    
    print("\n" + "=" * 60)
    if dashboard_success and len(activities) >= 0:
        print("✅ تم إصلاح مشكلة لوحة التحكم بنجاح!")
        print("🎉 يمكن الآن تشغيل التطبيق بدون أخطاء")
    else:
        print("❌ ما زالت هناك مشاكل تحتاج إصلاح")
    
    print("✅ انتهى الاختبار")
