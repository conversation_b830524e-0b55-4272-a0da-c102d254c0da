#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث عن مصدر خطأ int() مع البيانات المعطوبة
"""

import os
import re
import traceback

def search_for_int_usage():
    """البحث عن استخدامات int() في الكود"""
    print("🔍 البحث عن استخدامات int() في الكود...")
    
    # البحث في جميع ملفات Python
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"📁 تم العثور على {len(python_files)} ملف Python")
    
    # البحث عن استخدامات int() مع البيانات المشبوهة
    suspicious_patterns = [
        r'int\([^)]*date[^)]*\)',  # int() مع متغيرات تحتوي على date
        r'int\([^)]*time[^)]*\)',  # int() مع متغيرات تحتوي على time
        r'int\([^)]*trans[^)]*\)',  # int() مع متغيرات تحتوي على trans
        r'int\([^)]*\[.*\]\)',     # int() مع فهرسة قاموس أو قائمة
        r'datetime\.strptime.*int',  # datetime.strptime مع int
    ]
    
    found_issues = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    for pattern in suspicious_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            found_issues.append({
                                'file': file_path,
                                'line': line_num,
                                'content': line.strip(),
                                'pattern': pattern
                            })
        
        except Exception as e:
            print(f"⚠️ خطأ في قراءة {file_path}: {e}")
    
    # عرض النتائج
    if found_issues:
        print(f"\n🚨 تم العثور على {len(found_issues)} استخدام مشبوه لـ int():")
        
        for issue in found_issues:
            print(f"\n📄 {issue['file']}:{issue['line']}")
            print(f"   {issue['content']}")
            print(f"   نمط: {issue['pattern']}")
    else:
        print("✅ لم يتم العثور على استخدامات مشبوهة لـ int()")

def test_database_directly():
    """اختبار قاعدة البيانات مباشرة للعثور على البيانات المعطوبة"""
    print("\n🔍 اختبار قاعدة البيانات مباشرة...")
    
    try:
        import sqlite3
        from datetime import datetime
        
        # محاولة الاتصال بقواعد البيانات المختلفة
        db_paths = [
            'stores_management.db',
            'stores.db',
            'database.db'
        ]
        
        for db_path in db_paths:
            if not os.path.exists(db_path):
                continue
                
            print(f"\n📊 فحص قاعدة البيانات: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # فحص جدول المعاملات
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
                if cursor.fetchone():
                    print("✅ تم العثور على جدول transactions")
                    
                    # فحص البيانات
                    cursor.execute("SELECT id, transaction_date FROM transactions LIMIT 10")
                    transactions = cursor.fetchall()
                    
                    print(f"📊 عدد المعاملات: {len(transactions)}")
                    
                    for trans in transactions:
                        trans_id = trans['id']
                        trans_date = trans['transaction_date']
                        
                        print(f"  معاملة {trans_id}: {repr(trans_date)} (نوع: {type(trans_date)})")
                        
                        # محاولة تحليل التاريخ
                        try:
                            if isinstance(trans_date, str):
                                if '.' in trans_date:
                                    trans_date = trans_date.split('.')[0]
                                datetime.strptime(trans_date, '%Y-%m-%d %H:%M:%S')
                                print(f"    ✅ تاريخ صالح")
                            else:
                                print(f"    ❌ نوع بيانات خاطئ: {type(trans_date)}")
                        except Exception as e:
                            print(f"    ❌ خطأ في التاريخ: {e}")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ خطأ في فحص {db_path}: {e}")
    
    except Exception as e:
        print(f"❌ خطأ عام في فحص قاعدة البيانات: {e}")

def simulate_dashboard_loading():
    """محاكاة تحميل لوحة التحكم للعثور على مصدر الخطأ"""
    print("\n🧪 محاكاة تحميل لوحة التحكم...")
    
    try:
        # محاولة استيراد وتشغيل الكود المشبوه
        from database import db_manager
        
        # اختبار الاستعلام الذي يسبب المشكلة
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام المعاملات...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة")
        
        # فحص كل معاملة
        for i, trans in enumerate(transactions):
            print(f"\n📋 معاملة {i+1}:")
            for key, value in trans.items():
                print(f"  {key}: {repr(value)} (نوع: {type(value)})")
            
            # محاولة معالجة التاريخ
            try:
                date_str = trans['transaction_date']
                print(f"  🔍 معالجة التاريخ: {repr(date_str)}")
                
                if isinstance(date_str, str):
                    from datetime import datetime
                    if '.' in date_str:
                        date_str = date_str.split('.')[0]
                    
                    if ' ' in date_str:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        print(f"    ✅ تم تحليل التاريخ: {trans_date}")
                    else:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        print(f"    ✅ تم تحليل التاريخ: {trans_date}")
                else:
                    print(f"    ❌ نوع بيانات خاطئ: {type(date_str)}")
            
            except Exception as e:
                print(f"    ❌ خطأ في معالجة التاريخ: {e}")
                print(f"    📍 تفاصيل الخطأ:")
                traceback.print_exc()
            
            # محاولة معالجة عدد الأصناف
            try:
                items_count = trans['items_count']
                print(f"  🔍 معالجة عدد الأصناف: {repr(items_count)}")
                
                if items_count:
                    count_int = int(float(items_count))
                    print(f"    ✅ تم تحويل العدد: {count_int}")
                else:
                    print(f"    ⚠️ عدد فارغ")
            
            except Exception as e:
                print(f"    ❌ خطأ في معالجة العدد: {e}")
                print(f"    📍 تفاصيل الخطأ:")
                traceback.print_exc()
    
    except Exception as e:
        print(f"❌ خطأ في محاكاة لوحة التحكم: {e}")
        print(f"📍 تفاصيل الخطأ:")
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 البحث عن مصدر خطأ int() مع البيانات المعطوبة")
    print("=" * 60)
    
    # البحث في الكود
    search_for_int_usage()
    
    # فحص قاعدة البيانات
    test_database_directly()
    
    # محاكاة تحميل لوحة التحكم
    simulate_dashboard_loading()
    
    print("\n" + "=" * 60)
    print("✅ انتهى البحث")
