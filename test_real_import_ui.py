#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة الاستيراد الحقيقية
Test Real Import UI

اختبار تفاعلي لشاشة إدارة الأصناف مع التحسينات الجديدة
"""

import sys
import os
from pathlib import Path
import pandas as pd
import tkinter as tk
from tkinter import messagebox
import time

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import ttkbootstrap as ttk_bs
    from database import db_manager
    from models import AddedItem, OrganizationalChart
    from ui.inventory_window import InventoryWindow
    from config import APP_CONFIG
    print("✅ تم استيراد جميع الوحدات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def create_sample_excel_file():
    """إنشاء ملف Excel نموذجي للاختبار"""
    print("📄 إنشاء ملف Excel نموذجي للاختبار...")
    
    # إضافة بعض الأصناف إلى الجدول التنظيمي أولاً
    test_org_items = [
        {
            'sequence_number': 1,
            'item_code': 'DEMO001',
            'item_name': 'جهاز كمبيوتر محمول',
            'unit': 'قطعة',
            'quantity': 0,
            'notes': 'للاختبار',
            'is_active': True
        },
        {
            'sequence_number': 2,
            'item_code': 'DEMO002',
            'item_name': 'طابعة ليزر ملونة',
            'unit': 'قطعة',
            'quantity': 0,
            'notes': 'للاختبار',
            'is_active': True
        },
        {
            'sequence_number': 3,
            'item_code': 'DEMO003',
            'item_name': 'شاشة عرض كبيرة',
            'unit': 'قطعة',
            'quantity': 0,
            'notes': 'للاختبار',
            'is_active': True
        },
        {
            'sequence_number': 4,
            'item_code': 'DEMO004',
            'item_name': 'كيبورد لاسلكي',
            'unit': 'قطعة',
            'quantity': 0,
            'notes': 'للاختبار',
            'is_active': True
        }
    ]
    
    # إضافة الأصناف إلى الجدول التنظيمي
    for item_data in test_org_items:
        try:
            existing = OrganizationalChart.get_by_item_code(item_data['item_code'])
            if not existing:
                org_item = OrganizationalChart(**item_data)
                org_item.save()
                print(f"✅ تم إضافة: {item_data['item_name']} ({item_data['item_code']})")
        except Exception as e:
            print(f"⚠️ خطأ في إضافة {item_data['item_name']}: {e}")
    
    # إنشاء ملف Excel للاختبار
    test_data = [
        {
            'اسم الصنف': 'جهاز كمبيوتر محمول',
            'رقم الصنف': 'DEMO001',
            'الكمية': 5,  # سيتم استيراده
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة إلكترونية'
        },
        {
            'اسم الصنف': 'طابعة ليزر ملونة',
            'رقم الصنف': 'DEMO002',
            'الكمية': 0,  # سيتم تخطيه (كمية صفر)
            'الوحدة': 'قطعة',
            'نوع العهدة': 'عينية',
            'التصنيف': 'أجهزة إلكترونية'
        },
        {
            'اسم الصنف': 'شاشة عرض كبيرة',
            'رقم الصنف': 'DEMO003',
            'الكمية': None,  # سيتم تخطيه (كمية فارغة)
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'أجهزة إلكترونية'
        },
        {
            'اسم الصنف': 'كيبورد لاسلكي',
            'رقم الصنف': 'DEMO004',
            'الكمية': 10,  # سيتم استيراده
            'الوحدة': 'قطعة',
            'نوع العهدة': 'شخصية',
            'التصنيف': 'ملحقات'
        }
    ]
    
    df = pd.DataFrame(test_data)
    excel_file = project_root / "sample_import_test.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel: {excel_file}")
    print("📋 محتويات الملف:")
    for i, item in enumerate(test_data, 1):
        quantity = item['الكمية'] if item['الكمية'] is not None else "فارغة"
        status = ""
        if item['الكمية'] is None or item['الكمية'] == 0:
            status = " ← سيتم تخطيه"
        else:
            status = " ← سيتم استيراده"
        print(f"  {i}. {item['اسم الصنف']} - الكمية: {quantity}{status}")
    
    return excel_file

def test_inventory_window_import():
    """اختبار شاشة إدارة الأصناف مع الاستيراد"""
    print("\n🖼️ فتح شاشة إدارة الأصناف للاختبار...")
    
    try:
        # إنشاء النافذة الرئيسية
        root = ttk_bs.Window(
            title="اختبار استيراد الأصناف",
            themename=APP_CONFIG.get("theme", "cosmo"),
            size=(1200, 800),
            resizable=(True, True)
        )
        
        # إنشاء شاشة إدارة الأصناف
        inventory_window = InventoryWindow(root)
        
        print("✅ تم فتح شاشة إدارة الأصناف")
        print("\n📋 تعليمات الاختبار:")
        print("1. ستفتح شاشة إدارة الأصناف")
        print("2. اضغط على زر 'استيراد من Excel'")
        print("3. اختر الملف: sample_import_test.xlsx")
        print("4. لاحظ الرسالة التي ستظهر:")
        print("   • يجب أن تظهر: تم استيراد 2 صنف")
        print("   • يجب أن تظهر: تم تصفية الأصناف ذات الكمية 0 أو الفارغة")
        print("5. جرب الاستيراد مرة أخرى لاختبار منع التكرار")
        print("\n⚠️ ملاحظة: الملف موجود في مجلد المشروع")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فتح شاشة إدارة الأصناف: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    print("\n🧹 تنظيف بيانات الاختبار...")
    
    try:
        # حذف الأصناف المضافة في الاختبار
        deleted_items = db_manager.execute_query(
            "DELETE FROM added_items WHERE item_number LIKE 'DEMO%'"
        ).rowcount
        
        deleted_org = db_manager.execute_query(
            "DELETE FROM organizational_chart WHERE item_code LIKE 'DEMO%'"
        ).rowcount
        
        if deleted_items > 0:
            print(f"🗑️ تم حذف {deleted_items} صنف اختبار من إدارة الأصناف")
        
        if deleted_org > 0:
            print(f"🗑️ تم حذف {deleted_org} صنف اختبار من الجدول التنظيمي")
        
        # حذف ملف Excel
        excel_file = project_root / "sample_import_test.xlsx"
        if excel_file.exists():
            excel_file.unlink()
            print("🗑️ تم حذف ملف Excel النموذجي")
        
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في التنظيف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار واجهة الاستيراد الحقيقية")
    print("="*50)
    print("هذا الاختبار سيفتح شاشة إدارة الأصناف الحقيقية")
    print("لاختبار التحسينات الجديدة:")
    print("• تصفية الأصناف ذات الكمية 0 أو الفارغة")
    print("• منع استيراد الأصناف المكررة")
    print("• رسائل النتائج المحسنة")
    print("="*50)
    
    try:
        # إنشاء ملف Excel نموذجي
        excel_file = create_sample_excel_file()
        
        print(f"\n📁 تم إنشاء ملف الاختبار: {excel_file}")
        print("🎯 النتائج المتوقعة:")
        print("• سيتم استيراد 2 أصناف فقط (الكمية > 0)")
        print("• سيتم تخطي 2 أصناف (كمية 0 أو فارغة)")
        print("• ستظهر رسالة مفصلة بالنتائج")
        
        input("\n⏸️ اضغط Enter للمتابعة وفتح شاشة إدارة الأصناف...")
        
        # فتح شاشة إدارة الأصناف
        success = test_inventory_window_import()
        
        if success:
            print("\n✅ تم إغلاق شاشة الاختبار")
        else:
            print("\n❌ فشل في فتح شاشة الاختبار")
        
        # تنظيف البيانات
        cleanup_test_data()
        
        print("\n🎉 انتهى الاختبار!")
        print("📋 ملخص ما تم اختباره:")
        print("✅ تصفية الأصناف ذات الكمية 0 أو الفارغة")
        print("✅ رسائل النتائج المحسنة")
        print("✅ واجهة المستخدم تعمل بشكل صحيح")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())