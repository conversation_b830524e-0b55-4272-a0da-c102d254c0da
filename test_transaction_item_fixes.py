#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات إضافة الأصناف في تفاصيل العملية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import AddedItem, Transaction, TransactionItem, InventoryMovement

def test_transaction_item_functionality():
    """اختبار وظائف إضافة الأصناف في العمليات"""
    print("🧪 بدء اختبار وظائف إضافة الأصناف في العمليات...")
    
    try:
        # 1. التحقق من وجود أصناف في المخزون
        print("\n📦 فحص الأصناف المتاحة...")
        items = AddedItem.get_all()
        if not items:
            print("❌ لا توجد أصناف في المخزون للاختبار")
            return False
        
        print(f"✅ تم العثور على {len(items)} صنف في المخزون")
        
        # اختيار أول صنف للاختبار
        test_item = items[0]
        print(f"🎯 سيتم اختبار الصنف: {test_item.item_name} ({test_item.item_number})")
        print(f"   الكمية الحالية: {test_item.current_quantity}")
        
        if test_item.current_quantity <= 0:
            print("⚠️ الصنف المختار ليس له كمية متاحة، سيتم إضافة كمية للاختبار...")
            # إضافة كمية للاختبار
            test_item.current_quantity = 10
            test_item.save()
            print(f"✅ تم تحديث الكمية إلى: {test_item.current_quantity}")
        
        # 2. إنشاء معاملة اختبار
        print("\n📋 إنشاء معاملة اختبار...")
        from datetime import datetime

        transaction = Transaction()
        transaction.transaction_number = "TEST-001"
        transaction.beneficiary_name = "اختبار النظام"
        transaction.beneficiary_number = "TEST001"
        transaction.beneficiary_unit = "وحدة الاختبار"
        transaction.beneficiary_department = "قسم الاختبار"
        transaction.transaction_date = datetime.now()
        transaction.transaction_type = "صرف"
        transaction.status = "مكتملة"
        transaction.notes = "معاملة اختبار لفحص إضافة الأصناف"
        transaction.is_active = True
        
        if transaction.save():
            print(f"✅ تم إنشاء المعاملة بنجاح - ID: {transaction.id}")
        else:
            print("❌ فشل في إنشاء المعاملة")
            return False
        
        # 3. محاكاة إضافة صنف للمعاملة (كما يحدث في الواجهة)
        print("\n🔄 محاكاة إضافة صنف للمعاملة...")
        
        # الكمية قبل الإضافة
        old_quantity = test_item.current_quantity
        test_quantity = 2
        
        # إضافة الصنف للمعاملة
        insert_query = """
            INSERT INTO transaction_items (transaction_id, item_id, quantity, notes, created_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        db_manager.execute_query(insert_query, (transaction.id, test_item.id, test_quantity, "اختبار إضافة صنف"))
        
        # تحديث كمية الصنف
        new_quantity = max(0, old_quantity - test_quantity)
        db_manager.execute_query("""
            UPDATE added_items 
            SET current_quantity = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, (new_quantity, test_item.id))
        
        print(f"✅ تم تحديث كمية الصنف: {old_quantity} -> {new_quantity}")
        
        # إنشاء حركة مخزون
        movement = InventoryMovement()
        movement.item_number = test_item.item_number
        movement.movement_type = "صرف"
        movement.quantity = test_quantity
        movement.organization_type = "عملية صرف"
        movement.organization_name = f"معاملة رقم {transaction.id}"
        movement.notes = "صرف من تفاصيل العملية - اختبار"
        movement.is_active = True
        
        # الحصول على معرف المستخدم
        try:
            current_user = db_manager.fetch_one("""
                SELECT id FROM users
                WHERE is_active = 1
                ORDER BY last_login DESC
                LIMIT 1
            """)
            if current_user:
                movement.user_id = current_user[0]
        except:
            movement.user_id = None
        
        if movement.save_without_quantity_update():
            print(f"✅ تم إنشاء حركة صرف للصنف {test_item.item_number} بكمية {test_quantity}")
        else:
            print(f"⚠️ فشل في إنشاء حركة المخزون")
        
        # 4. التحقق من النتائج
        print("\n🔍 التحقق من النتائج...")
        
        # فحص أصناف المعاملة
        transaction_items = db_manager.fetch_all("""
            SELECT ti.*, ai.item_name, ai.item_number 
            FROM transaction_items ti
            JOIN added_items ai ON ti.item_id = ai.id
            WHERE ti.transaction_id = ?
        """, (transaction.id,))
        
        print(f"✅ عدد الأصناف في المعاملة: {len(transaction_items)}")
        for item in transaction_items:
            print(f"   - {item[5]} ({item[6]}): كمية {item[2]}")
        
        # فحص تحديث الكمية
        updated_item = AddedItem.get_by_id(test_item.id)
        print(f"✅ الكمية المحدثة للصنف: {updated_item.current_quantity}")
        
        # فحص حركات المخزون
        movements = db_manager.fetch_all("""
            SELECT * FROM inventory_movements_new
            WHERE item_number = ? AND movement_type = 'صرف'
            ORDER BY movement_date DESC
            LIMIT 5
        """, (test_item.item_number,))
        
        print(f"✅ عدد حركات الصرف للصنف: {len(movements)}")
        for movement in movements:
            print(f"   - {movement[2]} كمية {movement[3]} في {movement[9]}")
        
        print("\n🎉 تم الاختبار بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_transaction_item_functionality()
