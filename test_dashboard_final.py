#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للوحة التحكم مع الإصلاحات الجديدة
"""

def test_dashboard_functionality():
    """اختبار وظائف لوحة التحكم"""
    print("🧪 اختبار وظائف لوحة التحكم النهائي...")
    
    try:
        # محاكاة استدعاء refresh_dashboard_data من main_window
        print("🔄 محاكاة تحديث بيانات لوحة التحكم...")
        
        # إجبار إعادة الاتصال بقاعدة البيانات مع الإعدادات الجديدة
        from database import force_reconnect
        force_reconnect()
        
        # استيراد النماذج المطلوبة
        from models import OrganizationalChart, Transaction, AddedItem, Beneficiary
        
        print("✅ تم إجبار إعادة الاتصال واستيراد النماذج")
        
        # حساب الأصناف المنخفضة من الجدول التنظيمي (5 فأقل)
        low_stock_items = []
        low_stock_count = 0
        
        try:
            # الحصول على جميع الأصناف من AddedItem
            added_items = AddedItem.get_all()
            for item in added_items:
                # حساب الكمية الحالية
                current_qty = item.current_quantity if hasattr(item, 'current_quantity') else 0
                try:
                    current_qty = int(float(current_qty)) if current_qty else 0
                except (ValueError, TypeError):
                    current_qty = 0
                
                # إضافة للأصناف منخفضة المخزون إذا كانت الكمية 5 أو أقل
                if current_qty <= 5:
                    low_stock_items.append({
                        'name': item.item_name,
                        'quantity': current_qty
                    })
                    low_stock_count += 1
        except Exception as e:
            print(f"خطأ في حساب الأصناف منخفضة المخزون: {e}")
        
        # حساب إجمالي الأصناف من الجدول التنظيمي
        try:
            total_items = len(OrganizationalChart.get_all(active_only=True))
        except Exception as e:
            print(f"خطأ في حساب إجمالي الأصناف: {e}")
            total_items = 0

        # حساب إجمالي المستفيدين
        try:
            total_beneficiaries = len(Beneficiary.get_all(active_only=True))
        except Exception as e:
            print(f"خطأ في حساب إجمالي المستفيدين: {e}")
            total_beneficiaries = 0

        # حساب إجمالي عمليات الصرف
        try:
            total_transactions = len(Transaction.get_all(status='مكتملة'))
        except Exception as e:
            print(f"خطأ في حساب إجمالي عمليات الصرف: {e}")
            total_transactions = 0
        
        # حساب الأصناف التي تحتاج تموين (كمية 5 أو أقل)
        supply_needed_count = low_stock_count  # نفس العدد
        
        # إنشاء بيانات لوحة التحكم
        dashboard_data = {
            'total_items': total_items,
            'total_beneficiaries': total_beneficiaries,
            'total_transactions': total_transactions,
            'low_stock_count': low_stock_count,
            'supply_needed_count': supply_needed_count,
            'low_stock_items': low_stock_items[:5]  # أول 5 أصناف فقط
        }
        
        print(f"\n📊 نتائج بيانات لوحة التحكم:")
        print(f"  - إجمالي أصناف الجدول التنظيمي: {dashboard_data['total_items']}")
        print(f"  - إجمالي المستفيدين: {dashboard_data['total_beneficiaries']}")
        print(f"  - إجمالي عمليات الصرف: {dashboard_data['total_transactions']}")
        print(f"  - الأصناف منخفضة المخزون: {dashboard_data['low_stock_count']}")
        print(f"  - الأصناف التي تحتاج تموين: {dashboard_data['supply_needed_count']}")
        
        if dashboard_data['low_stock_items']:
            print(f"\n📋 أمثلة على الأصناف منخفضة المخزون:")
            for item in dashboard_data['low_stock_items']:
                print(f"    - {item['name']}: {item['quantity']}")
        
        # التحقق من أن جميع البيانات موجودة
        if (dashboard_data['total_items'] > 0 and 
            dashboard_data['total_beneficiaries'] > 0 and 
            dashboard_data['total_transactions'] >= 0):
            print("\n🎉 جميع بيانات لوحة التحكم متوفرة وصحيحة!")
            return True, dashboard_data
        else:
            print("\n⚠️ بعض بيانات لوحة التحكم مفقودة")
            return False, dashboard_data
    
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة التحكم: {e}")
        import traceback
        traceback.print_exc()
        return False, {}

def test_get_real_activities():
    """اختبار دالة get_real_activities"""
    print("\n🧪 اختبار دالة get_real_activities...")
    
    try:
        from database import db_manager
        from datetime import datetime
        
        # تنفيذ نفس الاستعلام الموجود في get_real_activities
        transactions_query = """
            SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                   COUNT(ti.id) as items_count
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            WHERE t.status = 'مكتملة'
            GROUP BY t.id
            ORDER BY t.transaction_date DESC, t.id DESC
            LIMIT 5
        """
        
        print("🔄 تنفيذ استعلام الأنشطة...")
        transactions = db_manager.fetch_all(transactions_query)
        print(f"✅ تم تحميل {len(transactions)} معاملة")
        
        activities = []
        
        for trans in transactions:
            try:
                date_str = trans['transaction_date']
                
                # معالجة البيانات المعطوبة (نفس الكود المحدث في main_window.py)
                if date_str and isinstance(date_str, bytes):
                    try:
                        date_str = date_str.decode('utf-8')
                    except:
                        date_str = None
                
                if date_str and isinstance(date_str, str):
                    if date_str.startswith("b'") or date_str.startswith('b"'):
                        date_str = None
                    elif not date_str.replace('-', '').replace(':', '').replace(' ', '').replace('.', '').isdigit():
                        date_str = None
                
                if isinstance(date_str, str) and date_str:
                    try:
                        if '.' in date_str:
                            date_str = date_str.split('.')[0]
                        
                        if ' ' in date_str:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        
                        time_str = trans_date.strftime('%H:%M')
                        date_display = trans_date.strftime('%m/%d')
                    except (ValueError, TypeError) as e:
                        print(f"خطأ في معالجة التاريخ: {e} - التاريخ: {repr(date_str)}")
                        time_str = "00:00"
                        date_display = "اليوم"
                else:
                    time_str = "00:00"
                    date_display = "اليوم"

                beneficiary = trans['beneficiary_name'] or "غير محدد"
                try:
                    items_count = int(float(trans['items_count'])) if trans['items_count'] else 0
                except (ValueError, TypeError):
                    items_count = 0
                
                activity = {
                    'type': 'transaction',
                    'description': f"عملية صرف - {beneficiary}",
                    'details': f"{items_count} صنف",
                    'time': time_str,
                    'date': date_display
                }
                
                activities.append(activity)
                print(f"  ✅ نشاط: {activity['description']} - {activity['time']}")
            
            except Exception as e:
                print(f"  ❌ خطأ في معالجة المعاملة: {e}")
        
        print(f"✅ تم إنشاء {len(activities)} نشاط بنجاح")
        return activities
    
    except Exception as e:
        print(f"❌ خطأ في اختبار get_real_activities: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    print("🚀 اختبار نهائي للوحة التحكم مع الإصلاحات الجديدة")
    print("=" * 70)
    
    # اختبار وظائف لوحة التحكم
    dashboard_success, dashboard_data = test_dashboard_functionality()
    
    # اختبار get_real_activities
    activities = test_get_real_activities()
    
    print("\n" + "=" * 70)
    print("📋 ملخص النتائج:")
    
    if dashboard_success:
        print("✅ بيانات لوحة التحكم: تعمل بشكل مثالي")
        print(f"   - إجمالي أصناف الجدول التنظيمي: {dashboard_data.get('total_items', 0)}")
        print(f"   - إجمالي المستفيدين: {dashboard_data.get('total_beneficiaries', 0)}")
        print(f"   - إجمالي عمليات الصرف: {dashboard_data.get('total_transactions', 0)}")
        print(f"   - الأصناف منخفضة المخزون: {dashboard_data.get('low_stock_count', 0)}")
        print(f"   - الأصناف التي تحتاج تموين: {dashboard_data.get('supply_needed_count', 0)}")
    else:
        print("❌ بيانات لوحة التحكم: تحتاج إصلاح")
    
    if len(activities) >= 0:
        print(f"✅ آخر الأنشطة: تعمل بشكل مثالي ({len(activities)} نشاط)")
    else:
        print("❌ آخر الأنشطة: تحتاج إصلاح")
    
    if dashboard_success and len(activities) >= 0:
        print("\n🎉 تم إصلاح جميع مشاكل لوحة التحكم بنجاح!")
        print("✅ يمكن الآن تشغيل التطبيق وستظهر جميع البيانات في لوحة التحكم")
        print("📝 التحديثات المطبقة:")
        print("   - تعطيل detect_types في config.py")
        print("   - إضافة force_reconnect() في database.py")
        print("   - تحديث refresh_dashboard_data() في ui/main_window.py")
    else:
        print("\n❌ ما زالت هناك مشاكل تحتاج إصلاح")
    
    print("✅ انتهى الاختبار النهائي")
