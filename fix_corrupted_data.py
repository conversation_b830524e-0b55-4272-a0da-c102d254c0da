#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح البيانات المعطوبة في قاعدة البيانات
"""

from database import db_manager
from datetime import datetime

def fix_corrupted_transactions():
    """إصلاح البيانات المعطوبة في جدول المعاملات"""
    print("🔧 بدء إصلاح البيانات المعطوبة في جدول المعاملات...")
    
    try:
        # البحث عن السجلات المعطوبة
        corrupted_transactions = db_manager.fetch_all("""
            SELECT id, transaction_date 
            FROM transactions 
            WHERE transaction_date LIKE '%06 17:47:37%'
        """)
        
        print(f"📊 تم العثور على {len(corrupted_transactions)} معاملة معطوبة")
        
        if not corrupted_transactions:
            print("✅ لا توجد معاملات معطوبة")
            return
        
        # عرض البيانات المعطوبة
        for i, trans in enumerate(corrupted_transactions):
            print(f"معاملة معطوبة {i+1}: ID={trans['id']}, تاريخ={trans['transaction_date']}")
        
        # إصلاح البيانات
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for trans in corrupted_transactions:
            try:
                # محاولة استخراج التاريخ الصحيح
                corrupted_date = trans['transaction_date']
                
                # إذا كان التاريخ يحتوي على البيانات المعطوبة، استبدله بتاريخ صحيح
                if 'b\'06 17:47:37' in str(corrupted_date):
                    # استخدام تاريخ اليوم كبديل
                    fixed_date = current_date
                else:
                    # محاولة إصلاح التاريخ
                    fixed_date = current_date
                
                # تحديث السجل
                db_manager.execute_query("""
                    UPDATE transactions 
                    SET transaction_date = ? 
                    WHERE id = ?
                """, (fixed_date, trans['id']))
                
                print(f"✅ تم إصلاح المعاملة {trans['id']}: {corrupted_date} -> {fixed_date}")
                
            except Exception as e:
                print(f"❌ فشل في إصلاح المعاملة {trans['id']}: {e}")
        
        print("✅ تم الانتهاء من إصلاح المعاملات")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المعاملات: {e}")

def fix_corrupted_inventory_movements():
    """إصلاح البيانات المعطوبة في جدول حركات المخزون"""
    print("\n🔧 فحص حركات المخزون...")
    
    try:
        # فحص حركات المخزون للبحث عن بيانات معطوبة
        movements = db_manager.fetch_all("""
            SELECT id, movement_date, quantity 
            FROM inventory_movements_new 
            LIMIT 10
        """)
        
        corrupted_count = 0
        for mov in movements:
            try:
                # محاولة تحويل الكمية إلى رقم
                int(float(mov['quantity']))
            except (ValueError, TypeError):
                corrupted_count += 1
                print(f"حركة معطوبة: ID={mov['id']}, كمية={mov['quantity']}")
        
        if corrupted_count == 0:
            print("✅ جميع حركات المخزون سليمة")
        else:
            print(f"⚠️ تم العثور على {corrupted_count} حركة معطوبة")
            
    except Exception as e:
        print(f"❌ خطأ في فحص حركات المخزون: {e}")

def fix_corrupted_beneficiaries():
    """إصلاح البيانات المعطوبة في جدول المستفيدين"""
    print("\n🔧 فحص المستفيدين...")
    
    try:
        # فحص المستفيدين للبحث عن بيانات معطوبة
        beneficiaries = db_manager.fetch_all("""
            SELECT id, created_at 
            FROM beneficiaries 
            WHERE created_at LIKE '%06 17:47:37%'
        """)
        
        if len(beneficiaries) == 0:
            print("✅ جميع بيانات المستفيدين سليمة")
        else:
            print(f"⚠️ تم العثور على {len(beneficiaries)} مستفيد بتاريخ معطوب")
            
            # إصلاح التواريخ المعطوبة
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            for ben in beneficiaries:
                try:
                    db_manager.execute_query("""
                        UPDATE beneficiaries 
                        SET created_at = ?, updated_at = ?
                        WHERE id = ?
                    """, (current_date, current_date, ben['id']))
                    
                    print(f"✅ تم إصلاح المستفيد {ben['id']}")
                    
                except Exception as e:
                    print(f"❌ فشل في إصلاح المستفيد {ben['id']}: {e}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص المستفيدين: {e}")

def verify_fixes():
    """التحقق من نجاح الإصلاحات"""
    print("\n🔍 التحقق من نجاح الإصلاحات...")
    
    try:
        # فحص المعاملات
        corrupted_transactions = db_manager.fetch_all("""
            SELECT COUNT(*) as count 
            FROM transactions 
            WHERE transaction_date LIKE '%06 17:47:37%'
        """)
        
        print(f"المعاملات المعطوبة المتبقية: {corrupted_transactions[0]['count']}")
        
        # فحص المستفيدين
        corrupted_beneficiaries = db_manager.fetch_all("""
            SELECT COUNT(*) as count 
            FROM beneficiaries 
            WHERE created_at LIKE '%06 17:47:37%'
        """)
        
        print(f"المستفيدين المعطوبين المتبقيين: {corrupted_beneficiaries[0]['count']}")
        
        # اختبار تحميل البيانات
        print("\n🧪 اختبار تحميل البيانات...")
        
        # اختبار المعاملات
        try:
            transactions = db_manager.fetch_all("SELECT * FROM transactions LIMIT 5")
            print(f"✅ تم تحميل {len(transactions)} معاملة بنجاح")
        except Exception as e:
            print(f"❌ فشل في تحميل المعاملات: {e}")
        
        # اختبار المستفيدين
        try:
            beneficiaries = db_manager.fetch_all("SELECT * FROM beneficiaries LIMIT 5")
            print(f"✅ تم تحميل {len(beneficiaries)} مستفيد بنجاح")
        except Exception as e:
            print(f"❌ فشل في تحميل المستفيدين: {e}")
            
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")

if __name__ == "__main__":
    print("🚀 بدء إصلاح البيانات المعطوبة في قاعدة البيانات...")
    print("=" * 60)
    
    # إصلاح المعاملات
    fix_corrupted_transactions()
    
    # فحص حركات المخزون
    fix_corrupted_inventory_movements()
    
    # إصلاح المستفيدين
    fix_corrupted_beneficiaries()
    
    # التحقق من النتائج
    verify_fixes()
    
    print("\n" + "=" * 60)
    print("✅ انتهى إصلاح البيانات")
